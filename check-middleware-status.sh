#!/bin/bash

# Middleware Status Check Script
# This script verifies that all middleware services are running properly

echo "🔍 Middleware Status Check"
echo "=========================="

# Check if container is running
echo "📦 Container Status:"
if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep middleware > /dev/null; then
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep middleware
    echo "✅ Container is running"
else
    echo "❌ Container is not running"
    exit 1
fi

echo ""

# Check volumes
echo "💾 Volume Status:"
docker volume ls | grep middleware
echo "✅ Persistent volumes are created"

echo ""

# Check service endpoints
echo "🌐 Service Health Check:"

# Web Interface
WEB_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3333)
if [ "$WEB_STATUS" = "200" ]; then
    echo "✅ Web Interface (http://localhost:3333): $WEB_STATUS"
else
    echo "❌ Web Interface (http://localhost:3333): $WEB_STATUS"
fi

# Analytics API
API_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:9696)
if [ "$API_STATUS" = "200" ]; then
    echo "✅ Analytics API (http://localhost:9696): $API_STATUS"
else
    echo "❌ Analytics API (http://localhost:9696): $API_STATUS"
fi

# Sync API
SYNC_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:9697)
if [ "$SYNC_STATUS" = "200" ]; then
    echo "✅ Sync API (http://localhost:9697): $SYNC_STATUS"
else
    echo "❌ Sync API (http://localhost:9697): $SYNC_STATUS"
fi

echo ""

# Check internal services via container logs
echo "🔧 Internal Services Status:"
if docker exec middleware supervisorctl status > /dev/null 2>&1; then
    docker exec middleware supervisorctl status
else
    echo "❌ Unable to check internal services status"
fi

echo ""
echo "🎉 Middleware is ready to use!"
echo "📱 Access the web interface at: http://localhost:3333"
echo "📊 View logs with: docker logs -f middleware"
echo "🛑 Stop with: docker stop middleware"
