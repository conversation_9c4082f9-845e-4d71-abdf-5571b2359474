#!/bin/bash

# Middleware Local Build and Deployment Script
# This script builds the middleware Docker image locally and runs it

set -e

echo "🏗️ Building Middleware Docker Image Locally..."

# Create Docker volumes for persistent data
echo "📦 Creating Docker volumes..."
docker volume create middleware_postgres_data || echo "Volume middleware_postgres_data already exists"
docker volume create middleware_keys || echo "Volume middleware_keys already exists"

# Stop and remove existing container if it exists
echo "🛑 Stopping existing middleware container..."
docker stop middleware-local 2>/dev/null || echo "No existing container to stop"
docker rm middleware-local 2>/dev/null || echo "No existing container to remove"

# Build the Docker image
echo "🔨 Building Docker image from source..."
docker build -t middleware:local \
             --build-arg ENVIRONMENT=prod \
             --build-arg POSTGRES_DB_ENABLED=true \
             --build-arg DB_INIT_ENABLED=true \
             --build-arg REDIS_ENABLED=true \
             --build-arg BACKEND_ENABLED=true \
             --build-arg FRONTEND_ENABLED=true \
             --build-arg CRON_ENABLED=true \
             --build-arg BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ") \
             --build-arg MERGE_COMMIT_SHA=$(git rev-parse HEAD 2>/dev/null || echo "unknown") \
             .

# Run the middleware container
echo "🏃 Starting middleware container..."
docker run --name middleware-local \
           -p 3333:3333 \
           -p 9696:9696 \
           -p 9697:9697 \
           -v middleware_postgres_data:/var/lib/postgresql/data \
           -v middleware_keys:/app/keys \
           -d middleware:local

echo "✅ Middleware container started successfully!"
echo ""
echo "📋 Container Information:"
echo "  - Container Name: middleware-local"
echo "  - Web Interface: http://localhost:3333"
echo "  - Analytics API: http://localhost:9696"
echo "  - Sync API: http://localhost:9697"
echo ""
echo "📊 To view logs, run:"
echo "  docker logs -f middleware-local"
echo ""
echo "🛑 To stop the container, run:"
echo "  docker stop middleware-local"
echo ""
echo "🗑️ To remove all data (CAUTION: This will delete all your data!):"
echo "  docker volume rm middleware_postgres_data middleware_keys"
