#!/bin/bash

# Setup Verification Script
# This script checks if everything is configured correctly

DOMAIN="dormetrics.truxt.ai"
MIDDLEWARE_PORT="3333"
API_PORT="9696"
SYNC_PORT="9697"

echo "🔍 Checking Middleware + Nginx + SSL Setup"
echo "=========================================="

# Check if running as root for some checks
if [[ $EUID -eq 0 ]]; then
    IS_ROOT=true
else
    IS_ROOT=false
fi

# Function to check service status
check_service() {
    local service=$1
    if systemctl is-active --quiet $service; then
        echo "✅ $service is running"
        return 0
    else
        echo "❌ $service is not running"
        return 1
    fi
}

# Function to check port
check_port() {
    local port=$1
    local service=$2
    if netstat -tuln | grep -q ":$port "; then
        echo "✅ Port $port ($service) is listening"
        return 0
    else
        echo "❌ Port $port ($service) is not listening"
        return 1
    fi
}

# Function to check HTTP response
check_http() {
    local url=$1
    local expected_code=$2
    local description=$3
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$url" 2>/dev/null)
    if [ "$response" = "$expected_code" ]; then
        echo "✅ $description (HTTP $response)"
        return 0
    else
        echo "❌ $description (HTTP $response, expected $expected_code)"
        return 1
    fi
}

echo ""
echo "📋 System Services Check:"
echo "------------------------"

# Check Docker
if command -v docker &> /dev/null; then
    echo "✅ Docker is installed"
    if systemctl is-active --quiet docker; then
        echo "✅ Docker service is running"
    else
        echo "❌ Docker service is not running"
    fi
else
    echo "❌ Docker is not installed"
fi

# Check Nginx
if command -v nginx &> /dev/null; then
    echo "✅ Nginx is installed"
    check_service nginx
else
    echo "❌ Nginx is not installed"
fi

# Check Certbot
if command -v certbot &> /dev/null; then
    echo "✅ Certbot is installed"
else
    echo "❌ Certbot is not installed"
fi

echo ""
echo "🐳 Middleware Container Check:"
echo "-----------------------------"

# Check if Middleware container is running
if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q middleware; then
    echo "✅ Middleware container is running"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep middleware
else
    echo "❌ Middleware container is not running"
    echo "   Start it with: docker start middleware"
fi

echo ""
echo "🌐 Port Availability Check:"
echo "--------------------------"

check_port 80 "HTTP"
check_port 443 "HTTPS"
check_port $MIDDLEWARE_PORT "Middleware Web"
check_port $API_PORT "Middleware API"
check_port $SYNC_PORT "Middleware Sync"

echo ""
echo "🔗 DNS Resolution Check:"
echo "-----------------------"

DOMAIN_IP=$(dig +short $DOMAIN 2>/dev/null | tail -n1)
SERVER_IP=$(curl -s --connect-timeout 5 ifconfig.me 2>/dev/null || curl -s --connect-timeout 5 ipinfo.io/ip 2>/dev/null || echo "Unable to determine")

if [ -n "$DOMAIN_IP" ]; then
    echo "✅ Domain $DOMAIN resolves to: $DOMAIN_IP"
    echo "🖥️ Server IP: $SERVER_IP"
    
    if [ "$DOMAIN_IP" = "$SERVER_IP" ]; then
        echo "✅ DNS points to this server"
    else
        echo "⚠️ DNS does not point to this server"
    fi
else
    echo "❌ Domain $DOMAIN does not resolve"
fi

echo ""
echo "🔐 SSL Certificate Check:"
echo "------------------------"

if [ "$IS_ROOT" = true ]; then
    if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
        echo "✅ SSL certificate exists"
        
        # Check certificate expiry
        EXPIRY=$(openssl x509 -enddate -noout -in "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" | cut -d= -f2)
        EXPIRY_EPOCH=$(date -d "$EXPIRY" +%s)
        CURRENT_EPOCH=$(date +%s)
        DAYS_LEFT=$(( (EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))
        
        if [ $DAYS_LEFT -gt 30 ]; then
            echo "✅ Certificate expires in $DAYS_LEFT days"
        elif [ $DAYS_LEFT -gt 0 ]; then
            echo "⚠️ Certificate expires in $DAYS_LEFT days (renewal recommended)"
        else
            echo "❌ Certificate has expired!"
        fi
    else
        echo "❌ SSL certificate not found"
    fi
else
    echo "ℹ️ Run as root to check SSL certificate details"
fi

echo ""
echo "🌍 HTTP/HTTPS Connectivity Check:"
echo "--------------------------------"

# Check local services
check_http "http://localhost:$MIDDLEWARE_PORT" "200" "Local Middleware Web"
check_http "http://localhost:$API_PORT" "200" "Local Middleware API"
check_http "http://localhost:$SYNC_PORT" "200" "Local Middleware Sync"

# Check domain connectivity
if [ -n "$DOMAIN_IP" ]; then
    check_http "http://$DOMAIN" "301" "HTTP redirect to HTTPS"
    check_http "https://$DOMAIN" "200" "HTTPS access"
else
    echo "⏭️ Skipping domain checks (DNS not resolved)"
fi

echo ""
echo "📁 Nginx Configuration Check:"
echo "----------------------------"

if [ "$IS_ROOT" = true ]; then
    if [ -f "/etc/nginx/sites-available/$DOMAIN" ]; then
        echo "✅ Nginx site configuration exists"
    else
        echo "❌ Nginx site configuration missing"
    fi
    
    if [ -L "/etc/nginx/sites-enabled/$DOMAIN" ]; then
        echo "✅ Nginx site is enabled"
    else
        echo "❌ Nginx site is not enabled"
    fi
    
    # Test nginx configuration
    if nginx -t &>/dev/null; then
        echo "✅ Nginx configuration is valid"
    else
        echo "❌ Nginx configuration has errors"
        echo "   Run 'sudo nginx -t' for details"
    fi
else
    echo "ℹ️ Run as root to check nginx configuration"
fi

echo ""
echo "📊 Summary:"
echo "----------"

if docker ps --format "table {{.Names}}" | grep -q middleware && \
   systemctl is-active --quiet nginx && \
   [ -n "$DOMAIN_IP" ] && \
   curl -s --connect-timeout 10 "https://$DOMAIN" > /dev/null 2>&1; then
    echo "🎉 Setup appears to be working correctly!"
    echo "🌐 Access your Middleware at: https://$DOMAIN"
else
    echo "⚠️ Some issues detected. Please review the checks above."
fi

echo ""
echo "🔧 Useful Commands:"
echo "  Check Middleware logs: docker logs -f middleware"
echo "  Check Nginx logs: sudo tail -f /var/log/nginx/error.log"
echo "  Restart Nginx: sudo systemctl restart nginx"
echo "  Check SSL: sudo certbot certificates"
echo "  Renew SSL: sudo certbot renew"
