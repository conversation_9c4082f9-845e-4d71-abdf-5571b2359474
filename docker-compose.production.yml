version: '3.8'

volumes:
  postgres_data:
    driver: local
  middleware_keys:
    driver: local

services:
  middleware:
    container_name: middleware-prod
    image: middlewareeng/middleware:latest
    restart: unless-stopped
    
    ports:
      - "3333:3333"    # Web interface
      - "9696:9696"    # Analytics API
      - "9697:9697"    # Sync API
    
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - middleware_keys:/app/keys
    
    environment:
      - ENVIRONMENT=prod
      - POSTGRES_DB_ENABLED=true
      - DB_INIT_ENABLED=true
      - REDIS_ENABLED=true
      - BACKEND_ENABLED=true
      - FRONTEND_ENABLED=true
      - CRON_ENABLED=true
      - DB_HOST=localhost
      - DB_NAME=mhq-oss
      - DB_PASS=postgres
      - DB_PORT=5434
      - DB_USER=postgres
      - REDIS_HOST=localhost
      - REDIS_PORT=6385
      - PORT=3333
      - ANALYTICS_SERVER_PORT=9696
      - SYNC_SERVER_PORT=9697
      - INTERNAL_API_BASE_URL=http://localhost:9696
      - INTERNAL_SYNC_API_BASE_URL=http://localhost:9697
      - NEXT_PUBLIC_APP_ENVIRONMENT=prod
      - DEFAULT_SYNC_DAYS=31
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3333/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # Resource limits (adjust based on your server capacity)
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
