#!/bin/bash

# Cloudflare Origin Certificate Setup
# This script helps set up Cloudflare Origin Certificates for Full SSL mode

echo "🔐 Cloudflare Origin Certificate Setup"
echo "====================================="

DOMAIN="dormetrics.truxt.ai"

echo "📋 To use Full SSL mode with Cloudflare, you need to:"
echo ""
echo "1. 🌐 Go to Cloudflare Dashboard → SSL/TLS → Origin Server"
echo "2. 📜 Click 'Create Certificate'"
echo "3. 🏷️ Add hostnames: $DOMAIN, *.$DOMAIN"
echo "4. ⏰ Set validity period (15 years recommended)"
echo "5. 📥 Download the certificate and private key"
echo ""
echo "6. 💾 Save the files as:"
echo "   - Certificate: /etc/ssl/certs/cloudflare-origin.pem"
echo "   - Private Key: /etc/ssl/private/cloudflare-origin.key"
echo ""
echo "7. 🔧 Update nginx configuration to use these certificates"
echo ""

read -p "Do you want to proceed with manual certificate setup? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📁 Creating certificate directories..."
    sudo mkdir -p /etc/ssl/certs /etc/ssl/private
    
    echo ""
    echo "📝 Please paste the Cloudflare Origin Certificate content:"
    echo "(Paste the certificate, then press Ctrl+D when done)"
    echo ""
    
    sudo tee /etc/ssl/certs/cloudflare-origin.pem > /dev/null
    
    echo ""
    echo "🔑 Please paste the Private Key content:"
    echo "(Paste the private key, then press Ctrl+D when done)"
    echo ""
    
    sudo tee /etc/ssl/private/cloudflare-origin.key > /dev/null
    
    # Set proper permissions
    sudo chmod 644 /etc/ssl/certs/cloudflare-origin.pem
    sudo chmod 600 /etc/ssl/private/cloudflare-origin.key
    
    echo "✅ Certificates saved!"
    echo ""
    echo "🔧 Now updating nginx configuration..."
    
    # Create nginx config with SSL
    cat > /tmp/nginx-ssl.conf << 'EOF'
# Nginx configuration for Middleware with Cloudflare Origin Certificate

server {
    listen 80;
    server_name dormetrics.truxt.ai;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name dormetrics.truxt.ai;

    # Cloudflare Origin Certificate
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Real IP configuration for Cloudflare
    set_real_ip_from ************/20;
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from *************/18;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from **********/13;
    set_real_ip_from **********/22;
    real_ip_header CF-Connecting-IP;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Logging
    access_log /var/log/nginx/dormetrics.truxt.ai.access.log;
    error_log /var/log/nginx/dormetrics.truxt.ai.error.log;

    # Main application proxy
    location / {
        proxy_pass http://localhost:3333;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_cache_bypass $http_upgrade;
        
        # Cloudflare headers
        proxy_set_header CF-Connecting-IP $http_cf_connecting_ip;
        proxy_set_header CF-Ray $http_cf_ray;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }

    # API endpoints
    location /api/ {
        proxy_pass http://localhost:9696/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header CF-Connecting-IP $http_cf_connecting_ip;
    }

    # Sync API endpoints
    location /sync-api/ {
        proxy_pass http://localhost:9697/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header CF-Connecting-IP $http_cf_connecting_ip;
        
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }

    # Static files
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://localhost:3333;
        expires 1h;
        add_header Cache-Control "public";
    }

    # Health check
    location /health {
        proxy_pass http://localhost:3333/health;
        access_log off;
    }

    client_max_body_size 50M;
}
EOF

    sudo cp /tmp/nginx-ssl.conf /etc/nginx/sites-available/dormetrics.truxt.ai
    sudo nginx -t && sudo systemctl reload nginx
    
    echo "✅ Nginx configuration updated with SSL!"
    echo "🔧 Make sure to set Cloudflare SSL/TLS mode to 'Full'"
    
else
    echo "📝 Manual steps to fix the 526 error:"
    echo ""
    echo "🔧 OPTION 1 (Recommended - Easier):"
    echo "   1. Go to Cloudflare Dashboard"
    echo "   2. Select domain 'truxt.ai'"
    echo "   3. Go to SSL/TLS tab"
    echo "   4. Change from 'Full' to 'Flexible'"
    echo ""
    echo "🔧 OPTION 2 (More Secure):"
    echo "   1. Create Cloudflare Origin Certificate"
    echo "   2. Run this script again and choose 'y'"
    echo "   3. Keep SSL/TLS mode as 'Full'"
fi

echo ""
echo "🌐 After making changes, test with:"
echo "   curl -I https://dormetrics.truxt.ai"
