# Middleware Docker Deployment Guide

This guide provides multiple options for hosting the Middleware instance via Docker, from quick deployment using pre-built images to custom builds.

## Prerequisites

- Docker installed and running
- Docker Compose (for compose-based deployments)
- At least 4GB RAM available for the container
- Ports 3333, 9696, and 9697 available on your host

## Deployment Options

### Option 1: Quick Start with Pre-built Image (Recommended)

The fastest way to get Middleware running:

```bash
# Make the script executable
chmod +x docker-run-production.sh

# Run the deployment script
./docker-run-production.sh
```

Or manually:
```bash
# Create volumes
docker volume create middleware_postgres_data
docker volume create middleware_keys

# Run the container
docker run --name middleware \
           -p 3333:3333 \
           -p 9696:9696 \
           -p 9697:9697 \
           -v middleware_postgres_data:/var/lib/postgresql/data \
           -v middleware_keys:/app/keys \
           -d middlewareeng/middleware:latest
```

### Option 2: Docker Compose with Pre-built Image

```bash
# Start the service
docker-compose -f docker-compose.production.yml up -d

# View logs
docker-compose -f docker-compose.production.yml logs -f

# Stop the service
docker-compose -f docker-compose.production.yml down
```

### Option 3: Build from Source

If you want to build the image locally:

```bash
# Make the script executable
chmod +x docker-build-local.sh

# Run the build and deployment script
./docker-build-local.sh
```

### Option 4: Docker Compose with Local Build

```bash
# Set build metadata (optional)
export BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
export MERGE_COMMIT_SHA=$(git rev-parse HEAD)

# Build and start
docker-compose -f docker-compose.build.yml up -d --build

# View logs
docker-compose -f docker-compose.build.yml logs -f
```

## Accessing the Application

Once deployed, you can access:

- **Web Interface**: http://localhost:3333
- **Analytics API**: http://localhost:9696
- **Sync API**: http://localhost:9697

## Configuration

### Environment Variables

Key environment variables you can customize:

- `ENVIRONMENT`: Set to `prod` for production
- `DEFAULT_SYNC_DAYS`: Number of days to sync (default: 31)
- `DB_*`: Database configuration
- `REDIS_*`: Redis configuration
- `PORT`: Web server port (default: 3333)

### Persistent Data

The deployment uses Docker volumes for persistent data:

- `middleware_postgres_data`: Database data
- `middleware_keys`: Encryption keys and configuration

## Management Commands

### View Logs
```bash
# For script-based deployments
docker logs -f middleware

# For compose-based deployments
docker-compose -f docker-compose.production.yml logs -f
```

### Stop the Service
```bash
# For script-based deployments
docker stop middleware

# For compose-based deployments
docker-compose -f docker-compose.production.yml down
```

### Update to Latest Version
```bash
# Pull latest image and restart
docker pull middlewareeng/middleware:latest
docker stop middleware
docker rm middleware
./docker-run-production.sh
```

### Backup Data
```bash
# Backup database volume
docker run --rm -v middleware_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz -C /data .

# Backup keys volume
docker run --rm -v middleware_keys:/data -v $(pwd):/backup alpine tar czf /backup/keys_backup.tar.gz -C /data .
```

### Restore Data
```bash
# Restore database volume
docker run --rm -v middleware_postgres_data:/data -v $(pwd):/backup alpine tar xzf /backup/postgres_backup.tar.gz -C /data

# Restore keys volume
docker run --rm -v middleware_keys:/data -v $(pwd):/backup alpine tar xzf /backup/keys_backup.tar.gz -C /data
```

## Troubleshooting

### Container Won't Start
1. Check if ports are already in use: `netstat -tulpn | grep -E '(3333|9696|9697)'`
2. Check Docker logs: `docker logs middleware`
3. Ensure sufficient memory is available (minimum 4GB recommended)

### Database Issues
1. Check if database initialization completed: `docker logs middleware | grep -i postgres`
2. Verify database volume exists: `docker volume ls | grep middleware_postgres_data`

### Performance Issues
1. Increase memory limits in docker-compose files
2. Monitor resource usage: `docker stats middleware`

## Security Considerations

- Change default database passwords in production
- Use environment files for sensitive configuration
- Regularly update to the latest image version
- Implement proper backup strategies
- Consider using Docker secrets for sensitive data

## Production Recommendations

1. **Resource Allocation**: Allocate at least 4GB RAM and 2 CPU cores
2. **Monitoring**: Set up health checks and monitoring
3. **Backups**: Implement regular backup procedures
4. **Updates**: Establish an update strategy
5. **Security**: Use proper firewall rules and access controls
6. **SSL/TLS**: Consider using a reverse proxy with SSL termination
