ENVIRONMENT=dev
POSTGRES_DB_ENABLED=true
DB_INIT_ENABLED=true
REDIS_ENABLED=true
BACKEND_ENABLED=true
FRONTEND_ENABLED=true
CRON_ENABLED=true

DB_HOST=localhost
DB_NAME=mhq-oss
DB_PASS=postgres
DB_PORT=5434
DB_USER=postgres
REDIS_HOST=localhost
REDIS_PORT=6385
PORT=3333
SYNC_SERVER_PORT=9697
ANALYTICS_SERVER_PORT=9696
INTERNAL_API_BASE_URL=http://localhost:9696
INTERNAL_SYNC_API_BASE_URL=http://localhost:9697
NEXT_PUBLIC_APP_ENVIRONMENT="development"
DEFAULT_SYNC_DAYS=31
BUILD_DATE=2024-06-05T10:21:34Z
MERGE_COMMIT_SHA=5f9ff895ad1d7805edcb22bfe2fcc6129e33bd8c
