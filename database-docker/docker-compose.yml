services:
  db:
    image: postgres:14.3-alpine
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: mhq-oss
    ports:
      - "5434:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    command: ["postgres", "-c", "max_prepared_transactions=0"]

  redis:
    image: redis:latest
    restart: always
    ports:
      - "6385:6379"

  dbmate:
    build:
      context: ./
      dockerfile: Dockerfile
    depends_on:
      - db
    volumes:
      - ./db:/db
    environment:
      - DATABASE_URL=************************************/mhq-oss?sslmode=disable
    working_dir: /
    command: bash -c  "dbwait.sh && dbmate up && dbmate dump"

volumes:
  pgdata: {}
