#!/bin/bash

# Middleware Production Docker Deployment Script
# This script sets up and runs the middleware instance using the pre-built Docker image

set -e

echo "🚀 Starting Middleware Production Deployment..."

# Create Docker volumes for persistent data
echo "📦 Creating Docker volumes..."
docker volume create middleware_postgres_data || echo "Volume middleware_postgres_data already exists"
docker volume create middleware_keys || echo "Volume middleware_keys already exists"

# Stop and remove existing container if it exists
echo "🛑 Stopping existing middleware container..."
docker stop middleware 2>/dev/null || echo "No existing container to stop"
docker rm middleware 2>/dev/null || echo "No existing container to remove"

# Pull the latest image
echo "⬇️ Pulling latest middleware image..."
docker pull middlewareeng/middleware:latest

# Run the middleware container
echo "🏃 Starting middleware container..."
docker run --name middleware \
           -p 3333:3333 \
           -p 9696:9696 \
           -p 9697:9697 \
           -v middleware_postgres_data:/var/lib/postgresql/data \
           -v middleware_keys:/app/keys \
           -d middlewareeng/middleware:latest

echo "✅ Middleware container started successfully!"
echo ""
echo "📋 Container Information:"
echo "  - Container Name: middleware"
echo "  - Web Interface: http://localhost:3333"
echo "  - Analytics API: http://localhost:9696"
echo "  - Sync API: http://localhost:9697"
echo ""
echo "📊 To view logs, run:"
echo "  docker logs -f middleware"
echo ""
echo "🛑 To stop the container, run:"
echo "  docker stop middleware"
echo ""
echo "🗑️ To remove all data (CAUTION: This will delete all your data!):"
echo "  docker volume rm middleware_postgres_data middleware_keys"
