{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/public/*": ["./public/*"], "@/api/*": ["./pages/api/*"]}, "allowJs": true, "allowSyntheticDefaultImports": true, "jsx": "preserve", "lib": ["dom", "es2017", "ES2021", "es2021.intl", "ES2022"], "module": "esnext", "moduleResolution": "node", "noEmit": true, "noUnusedLocals": true, "noUnusedParameters": true, "preserveConstEnums": true, "removeComments": false, "skipLibCheck": true, "sourceMap": true, "strict": true, "strictPropertyInitialization": false, "strictNullChecks": false, "target": "esnext", "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "noFallthroughCasesInSwitch": true, "incremental": true, "plugins": [{"name": "next"}]}, "exclude": ["node_modules"], "include": ["src", "next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"]}