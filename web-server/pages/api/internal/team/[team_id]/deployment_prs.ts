export const mockDeploymentPrs = [
  {
    number: '129',
    title: 'Fix login issues',
    state: 'MERGED',
    first_response_time: 1070,
    rework_time: 0,
    merge_time: 302,
    cycle_time: 1372,
    author: {
      username: 'shivam-bit',
      linked_user: {
        id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
        name: '<PERSON><PERSON>',
        email: '<EMAIL>'
      }
    },
    reviewers: [
      {
        username: 'jayantbh'
      }
    ],
    repo_name: 'web-manager-dash',
    pr_link: 'https://github.com/monoclehq/web-manager-dash/pull/129',
    base_branch: 'main',
    head_branch: 'GROW-336',
    created_at: '2023-04-03T10:45:41+00:00',
    updated_at: '2023-04-03T11:08:33+00:00',
    state_changed_at: '2023-04-03T11:08:33+00:00',
    commits: 2,
    additions: 95,
    deletions: 30,
    changed_files: 8,
    comments: 1,
    provider: 'github',
    rework_cycles: 0
  }
];
