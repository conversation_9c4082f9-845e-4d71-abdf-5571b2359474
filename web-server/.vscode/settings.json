{"workbench.editor.labelFormat": "short", "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "editor.formatOnSave": false, "eslint.workingDirectories": [{"mode": "auto"}], "tsEssentialPlugins.autoImport.changeSorting": {"track": ["@/constants/events"], "useTheme": ["@mui/material"], "captureException": ["@sentry/node"], "useDispatch": ["@/store"], "useSelector": ["@/store"], "Line": ["@/components/Text", "../Text"], "createContext": ["react"]}, "tsEssentialPlugins.patchOutline": true, "editor.linkedEditing": true, "tsEssentialPlugins.enableMethodSnippets": false, "cSpell.words": ["collab", "openai", "opsgenie", "<PERSON><PERSON><PERSON><PERSON>", "Serie", "zenduty"]}