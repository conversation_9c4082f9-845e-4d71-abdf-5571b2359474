{"name": "web-manager-dash", "version": "0.0.1", "title": "Middleware HQ - Manager <PERSON>", "description": "Improve your engineering team productivity. Consistently.", "author": {"name": "Middleware HQ", "url": "https://www.middlewarehq.com/"}, "private": true, "dependencies": {"@emotion/cache": "11.7.1", "@emotion/react": "11.8.2", "@emotion/server": "11.4.0", "@emotion/styled": "11.8.1", "@faker-js/faker": "^6.0.0", "@hookform/resolvers": "^2.8.8", "@mui/icons-material": "^5.11.0", "@mui/lab": "^5.0.0-alpha.152", "@mui/material": "^5.14.17", "@mui/styles": "5.5.1", "@mui/x-date-pickers": "^6.18.1", "@mui/x-date-pickers-pro": "^6.18.1", "@nivo/bar": "^0.80.0", "@nivo/core": "^0.80.0", "@nivo/line": "0.80.0", "@nivo/tooltip": "^0.80.0", "@octokit/rest": "^18.12.0", "@reduxjs/toolkit": "1.8.0", "@svgr/webpack": "^6.2.1", "@types/jest": "^29.5.4", "axios": "1.7.7", "axios-hooks": "^5.0.2", "axios-retry": "^4.5.0", "chart.js": "^3.9.1", "chartjs-plugin-annotation": "^2.0.1", "chartjs-plugin-crosshair": "^1.2.0", "chartjs-plugin-gradient": "^0.5.1", "chartjs-plugin-trendline": "^2.0.0", "chartjs-plugin-zoom": "^1.2.1", "clsx": "^1.1.1", "date-fns": "^2.29.1", "fastest-validator": "^1.15.0", "fireworks-js": "^2.10.0", "framer-motion": "^6.3.3", "jest": "^29.6.4", "jest-environment-jsdom": "^29.6.4", "js-cookie": "^3.0.1", "knex": "^2.4.2", "next": "^15.0.1", "next-auth": "^4.23.0", "next-images": "1.8.4", "notistack": "^3.0.1", "nprogress": "0.2.0", "pg": "^8.9.0", "pluralize": "^8.0.0", "ramda": "^0.28.0", "react": "^18.3.1", "react-clamp-lines": "^3.0.2", "react-copy-to-clipboard": "^5.1.0", "react-custom-scrollbars-2": "4.4.0", "react-dom": "^18.3.1", "react-error-boundary": "^3.1.4", "react-hook-form": "^7.29.0", "react-icons": "^5.2.1", "react-markdown": "^9.0.1", "react-redux": "7.2.6", "redux": "4.1.2", "redux-persist": "^6.0.0", "redux-thunk": "2.4.1", "remark-gfm": "^4.0.0", "ts-jest": "^29.1.1", "typescript": "^5.2.2", "uuid": "8.3.2", "voca": "^1.4.0", "yup": "0.32.11"}, "scripts": {"knip": "knip", "prod-tun": "bash -c \"$PROD_TUNNEL\"", "stage-tun": "bash -c \"$STAGE_TUNNEL\"", "http": "node http-server", "dev": "[ -e ../.env ] && set -a && . ../.env; NEXT_MANUAL_SIG_HANDLE=true next -p $PORT", "dev-prod": "NEXT_MANUAL_SIG_HANDLE=true yarn env prod && next", "build": "./scripts/build.sh", "zip": "./scripts/zip.sh", "box-server-start-wo-install": "./scripts/server-init.sh", "box-server-start": "yarn install && yarn box-server-start-wo-install", "start": "NEXT_MANUAL_SIG_HANDLE=true next start", "export": "next export", "lint": "eslint src/**/*.{js,,ts,tsx}", "lint-fix": "eslint --fix --ignore-pattern 'node_modules/*' 'src/**/*.{js,ts,tsx}'", "format": "prettier --write \"./**/*.{ts,tsx,js,jsx,json}\" --config ./.prettierrc", "quality-check": "concurrently -c \"auto\" --kill-others-on-fail \"yarn lint-fix\" \"yarn tsc\"", "qc": "yarn quality-check", "clean": "rm -rf .next", "test": "jest --passWithNoTests --only<PERSON>hanged", "test:e2e": "playwright test --headed", "env": "bash ./scripts/setup-env.sh"}, "devDependencies": {"@next/bundle-analyzer": "^12.3.0", "@octokit/types": "^6.34.0", "@playwright/test": "^1.28.0", "@types/js-cookie": "^3.0.2", "@types/node": "^16.17.0", "@types/nprogress": "0.2.0", "@types/pluralize": "^0.0.29", "@types/ramda": "^0.28.7", "@types/react": "^17.0.41", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-dom": "^17.0.14", "@types/react-redux": "7.1.20", "@types/ua-parser-js": "^0.7.36", "@types/uuid": "8.3.3", "@types/vis": "^4.21.23", "@types/voca": "^1.4.2", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "concurrently": "^7.6.0", "dotenv": "^16.0.3", "eslint": "^8.40.0", "eslint-config-next": "15.0.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-unused-imports": "^3.1.0", "pm2": "^5.2.0", "prettier": "^3.0.3"}, "engines": {"node": "^22.x"}}