export const githubOrgsMock = [
  {
    login: 'kayako',
    id: 617018,
    node_id: 'MDEyOk9yZ2FuaXphdGlvbjYxNzAxOA==',
    url: 'https://api.github.com/orgs/kayako',
    repos_url: 'https://api.github.com/orgs/kayako/repos',
    events_url: 'https://api.github.com/orgs/kayako/events',
    hooks_url: 'https://api.github.com/orgs/kayako/hooks',
    issues_url: 'https://api.github.com/orgs/kayako/issues',
    members_url: 'https://api.github.com/orgs/kayako/members{/member}',
    public_members_url:
      'https://api.github.com/orgs/kayako/public_members{/member}',
    avatar_url: 'https://avatars.githubusercontent.com/u/617018?v=4',
    description: 'Unified Customer Service Platform',
    members: [
      {
        login: 'abdou-suleiman',
        id: 36932561,
        node_id: 'MDQ6VXNlcjM2OTMyNTYx',
        avatar_url: 'https://avatars.githubusercontent.com/u/36932561?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/abdou-suleiman',
        html_url: 'https://github.com/abdou-suleiman',
        followers_url: 'https://api.github.com/users/abdou-suleiman/followers',
        following_url:
          'https://api.github.com/users/abdou-suleiman/following{/other_user}',
        gists_url:
          'https://api.github.com/users/abdou-suleiman/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/abdou-suleiman/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/abdou-suleiman/subscriptions',
        organizations_url: 'https://api.github.com/users/abdou-suleiman/orgs',
        repos_url: 'https://api.github.com/users/abdou-suleiman/repos',
        events_url:
          'https://api.github.com/users/abdou-suleiman/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/abdou-suleiman/received_events',
        type: 'User',
        site_admin: false
      },
      {
        login: 'abhishekmittal',
        id: 2921303,
        node_id: 'MDQ6VXNlcjI5MjEzMDM=',
        avatar_url: 'https://avatars.githubusercontent.com/u/2921303?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/abhishekmittal',
        html_url: 'https://github.com/abhishekmittal',
        followers_url: 'https://api.github.com/users/abhishekmittal/followers',
        following_url:
          'https://api.github.com/users/abhishekmittal/following{/other_user}',
        gists_url:
          'https://api.github.com/users/abhishekmittal/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/abhishekmittal/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/abhishekmittal/subscriptions',
        organizations_url: 'https://api.github.com/users/abhishekmittal/orgs',
        repos_url: 'https://api.github.com/users/abhishekmittal/repos',
        events_url:
          'https://api.github.com/users/abhishekmittal/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/abhishekmittal/received_events',
        type: 'User',
        site_admin: false
      },
      {
        login: 'akcrossover',
        id: 37324472,
        node_id: 'MDQ6VXNlcjM3MzI0NDcy',
        avatar_url: 'https://avatars.githubusercontent.com/u/37324472?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/akcrossover',
        html_url: 'https://github.com/akcrossover',
        followers_url: 'https://api.github.com/users/akcrossover/followers',
        following_url:
          'https://api.github.com/users/akcrossover/following{/other_user}',
        gists_url: 'https://api.github.com/users/akcrossover/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/akcrossover/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/akcrossover/subscriptions',
        organizations_url: 'https://api.github.com/users/akcrossover/orgs',
        repos_url: 'https://api.github.com/users/akcrossover/repos',
        events_url: 'https://api.github.com/users/akcrossover/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/akcrossover/received_events',
        type: 'User',
        site_admin: false
      },
      {
        login: 'akshaykmr',
        id: 11945793,
        node_id: 'MDQ6VXNlcjExOTQ1Nzkz',
        avatar_url: 'https://avatars.githubusercontent.com/u/11945793?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/akshaykmr',
        html_url: 'https://github.com/akshaykmr',
        followers_url: 'https://api.github.com/users/akshaykmr/followers',
        following_url:
          'https://api.github.com/users/akshaykmr/following{/other_user}',
        gists_url: 'https://api.github.com/users/akshaykmr/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/akshaykmr/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/akshaykmr/subscriptions',
        organizations_url: 'https://api.github.com/users/akshaykmr/orgs',
        repos_url: 'https://api.github.com/users/akshaykmr/repos',
        events_url: 'https://api.github.com/users/akshaykmr/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/akshaykmr/received_events',
        type: 'User',
        site_admin: false
      },
      {
        login: 'anadirajtiwari',
        id: 7090322,
        node_id: 'MDQ6VXNlcjcwOTAzMjI=',
        avatar_url: 'https://avatars.githubusercontent.com/u/7090322?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/anadirajtiwari',
        html_url: 'https://github.com/anadirajtiwari',
        followers_url: 'https://api.github.com/users/anadirajtiwari/followers',
        following_url:
          'https://api.github.com/users/anadirajtiwari/following{/other_user}',
        gists_url:
          'https://api.github.com/users/anadirajtiwari/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/anadirajtiwari/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/anadirajtiwari/subscriptions',
        organizations_url: 'https://api.github.com/users/anadirajtiwari/orgs',
        repos_url: 'https://api.github.com/users/anadirajtiwari/repos',
        events_url:
          'https://api.github.com/users/anadirajtiwari/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/anadirajtiwari/received_events',
        type: 'User',
        site_admin: false
      }
    ],
    repos: [] as const
  },
  {
    login: 'monoclehq',
    id: 99589860,
    node_id: 'O_kgDOBe-e5A',
    url: 'https://api.github.com/orgs/monoclehq',
    repos_url: 'https://api.github.com/orgs/monoclehq/repos',
    events_url: 'https://api.github.com/orgs/monoclehq/events',
    hooks_url: 'https://api.github.com/orgs/monoclehq/hooks',
    issues_url: 'https://api.github.com/orgs/monoclehq/issues',
    members_url: 'https://api.github.com/orgs/monoclehq/members{/member}',
    public_members_url:
      'https://api.github.com/orgs/monoclehq/public_members{/member}',
    avatar_url: 'https://avatars.githubusercontent.com/u/99589860?v=4',
    description: null as null,
    members: [
      {
        login: 'dhruvagarwal',
        id: 4587641,
        node_id: 'MDQ6VXNlcjQ1ODc2NDE=',
        avatar_url: 'https://avatars.githubusercontent.com/u/4587641?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/dhruvagarwal',
        html_url: 'https://github.com/dhruvagarwal',
        followers_url: 'https://api.github.com/users/dhruvagarwal/followers',
        following_url:
          'https://api.github.com/users/dhruvagarwal/following{/other_user}',
        gists_url: 'https://api.github.com/users/dhruvagarwal/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/dhruvagarwal/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/dhruvagarwal/subscriptions',
        organizations_url: 'https://api.github.com/users/dhruvagarwal/orgs',
        repos_url: 'https://api.github.com/users/dhruvagarwal/repos',
        events_url:
          'https://api.github.com/users/dhruvagarwal/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/dhruvagarwal/received_events',
        type: 'User',
        site_admin: false
      },
      {
        login: 'jayantbh',
        id: 7949047,
        node_id: 'MDQ6VXNlcjc5NDkwNDc=',
        avatar_url: 'https://avatars.githubusercontent.com/u/7949047?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/jayantbh',
        html_url: 'https://github.com/jayantbh',
        followers_url: 'https://api.github.com/users/jayantbh/followers',
        following_url:
          'https://api.github.com/users/jayantbh/following{/other_user}',
        gists_url: 'https://api.github.com/users/jayantbh/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/jayantbh/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/jayantbh/subscriptions',
        organizations_url: 'https://api.github.com/users/jayantbh/orgs',
        repos_url: 'https://api.github.com/users/jayantbh/repos',
        events_url: 'https://api.github.com/users/jayantbh/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/jayantbh/received_events',
        type: 'User',
        site_admin: false
      },
      {
        login: 'MayankVir',
        id: 41983025,
        node_id: 'MDQ6VXNlcjQxOTgzMDI1',
        avatar_url: 'https://avatars.githubusercontent.com/u/41983025?v=4',
        gravatar_id: '',
        url: 'https://api.github.com/users/MayankVir',
        html_url: 'https://github.com/MayankVir',
        followers_url: 'https://api.github.com/users/MayankVir/followers',
        following_url:
          'https://api.github.com/users/MayankVir/following{/other_user}',
        gists_url: 'https://api.github.com/users/MayankVir/gists{/gist_id}',
        starred_url:
          'https://api.github.com/users/MayankVir/starred{/owner}{/repo}',
        subscriptions_url:
          'https://api.github.com/users/MayankVir/subscriptions',
        organizations_url: 'https://api.github.com/users/MayankVir/orgs',
        repos_url: 'https://api.github.com/users/MayankVir/repos',
        events_url: 'https://api.github.com/users/MayankVir/events{/privacy}',
        received_events_url:
          'https://api.github.com/users/MayankVir/received_events',
        type: 'User',
        site_admin: false
      }
    ],
    repos: [
      {
        id: 458772068,
        node_id: 'R_kgDOG1hOZA',
        name: 'monorepo',
        full_name: 'monoclehq/monorepo',
        private: true,
        owner: {
          login: 'monoclehq',
          id: 99589860,
          node_id: 'O_kgDOBe-e5A',
          avatar_url: 'https://avatars.githubusercontent.com/u/99589860?v=4',
          gravatar_id: '',
          url: 'https://api.github.com/users/monoclehq',
          html_url: 'https://github.com/monoclehq',
          followers_url: 'https://api.github.com/users/monoclehq/followers',
          following_url:
            'https://api.github.com/users/monoclehq/following{/other_user}',
          gists_url: 'https://api.github.com/users/monoclehq/gists{/gist_id}',
          starred_url:
            'https://api.github.com/users/monoclehq/starred{/owner}{/repo}',
          subscriptions_url:
            'https://api.github.com/users/monoclehq/subscriptions',
          organizations_url: 'https://api.github.com/users/monoclehq/orgs',
          repos_url: 'https://api.github.com/users/monoclehq/repos',
          events_url: 'https://api.github.com/users/monoclehq/events{/privacy}',
          received_events_url:
            'https://api.github.com/users/monoclehq/received_events',
          type: 'Organization',
          site_admin: false
        },
        html_url: 'https://github.com/monoclehq/monorepo',
        description: null,
        fork: false,
        url: 'https://api.github.com/repos/monoclehq/monorepo',
        forks_url: 'https://api.github.com/repos/monoclehq/monorepo/forks',
        keys_url:
          'https://api.github.com/repos/monoclehq/monorepo/keys{/key_id}',
        collaborators_url:
          'https://api.github.com/repos/monoclehq/monorepo/collaborators{/collaborator}',
        teams_url: 'https://api.github.com/repos/monoclehq/monorepo/teams',
        hooks_url: 'https://api.github.com/repos/monoclehq/monorepo/hooks',
        issue_events_url:
          'https://api.github.com/repos/monoclehq/monorepo/issues/events{/number}',
        events_url: 'https://api.github.com/repos/monoclehq/monorepo/events',
        assignees_url:
          'https://api.github.com/repos/monoclehq/monorepo/assignees{/user}',
        branches_url:
          'https://api.github.com/repos/monoclehq/monorepo/branches{/branch}',
        tags_url: 'https://api.github.com/repos/monoclehq/monorepo/tags',
        blobs_url:
          'https://api.github.com/repos/monoclehq/monorepo/git/blobs{/sha}',
        git_tags_url:
          'https://api.github.com/repos/monoclehq/monorepo/git/tags{/sha}',
        git_refs_url:
          'https://api.github.com/repos/monoclehq/monorepo/git/refs{/sha}',
        trees_url:
          'https://api.github.com/repos/monoclehq/monorepo/git/trees{/sha}',
        statuses_url:
          'https://api.github.com/repos/monoclehq/monorepo/statuses/{sha}',
        languages_url:
          'https://api.github.com/repos/monoclehq/monorepo/languages',
        stargazers_url:
          'https://api.github.com/repos/monoclehq/monorepo/stargazers',
        contributors_url:
          'https://api.github.com/repos/monoclehq/monorepo/contributors',
        subscribers_url:
          'https://api.github.com/repos/monoclehq/monorepo/subscribers',
        subscription_url:
          'https://api.github.com/repos/monoclehq/monorepo/subscription',
        commits_url:
          'https://api.github.com/repos/monoclehq/monorepo/commits{/sha}',
        git_commits_url:
          'https://api.github.com/repos/monoclehq/monorepo/git/commits{/sha}',
        comments_url:
          'https://api.github.com/repos/monoclehq/monorepo/comments{/number}',
        issue_comment_url:
          'https://api.github.com/repos/monoclehq/monorepo/issues/comments{/number}',
        contents_url:
          'https://api.github.com/repos/monoclehq/monorepo/contents/{+path}',
        compare_url:
          'https://api.github.com/repos/monoclehq/monorepo/compare/{base}...{head}',
        merges_url: 'https://api.github.com/repos/monoclehq/monorepo/merges',
        archive_url:
          'https://api.github.com/repos/monoclehq/monorepo/{archive_format}{/ref}',
        downloads_url:
          'https://api.github.com/repos/monoclehq/monorepo/downloads',
        issues_url:
          'https://api.github.com/repos/monoclehq/monorepo/issues{/number}',
        pulls_url:
          'https://api.github.com/repos/monoclehq/monorepo/pulls{/number}',
        milestones_url:
          'https://api.github.com/repos/monoclehq/monorepo/milestones{/number}',
        notifications_url:
          'https://api.github.com/repos/monoclehq/monorepo/notifications{?since,all,participating}',
        labels_url:
          'https://api.github.com/repos/monoclehq/monorepo/labels{/name}',
        releases_url:
          'https://api.github.com/repos/monoclehq/monorepo/releases{/id}',
        deployments_url:
          'https://api.github.com/repos/monoclehq/monorepo/deployments',
        created_at: '2022-02-13T10:09:16Z',
        updated_at: '2022-03-20T12:16:19Z',
        pushed_at: '2022-05-03T07:18:16Z',
        git_url: 'git://github.com/monoclehq/monorepo.git',
        ssh_url: '**************:monoclehq/monorepo.git',
        clone_url: 'https://github.com/monoclehq/monorepo.git',
        svn_url: 'https://github.com/monoclehq/monorepo',
        homepage: null,
        size: 166,
        stargazers_count: 0,
        watchers_count: 0,
        language: 'Python',
        has_issues: true,
        has_projects: true,
        has_downloads: true,
        has_wiki: true,
        has_pages: false,
        forks_count: 0,
        mirror_url: null as null,
        archived: false,
        disabled: false,
        open_issues_count: 0,
        license: null as null,
        allow_forking: false,
        is_template: false,
        topics: [] as const,
        visibility: 'private',
        forks: 0,
        open_issues: 0,
        watchers: 0,
        default_branch: 'master',
        permissions: {
          admin: true,
          maintain: true,
          push: true,
          triage: true,
          pull: true
        }
      },
      {
        id: 476066666,
        node_id: 'R_kgDOHGAzag',
        name: 'web-manager-dash',
        full_name: 'monoclehq/web-manager-dash',
        private: true,
        owner: {
          login: 'monoclehq',
          id: 99589860,
          node_id: 'O_kgDOBe-e5A',
          avatar_url: 'https://avatars.githubusercontent.com/u/99589860?v=4',
          gravatar_id: '',
          url: 'https://api.github.com/users/monoclehq',
          html_url: 'https://github.com/monoclehq',
          followers_url: 'https://api.github.com/users/monoclehq/followers',
          following_url:
            'https://api.github.com/users/monoclehq/following{/other_user}',
          gists_url: 'https://api.github.com/users/monoclehq/gists{/gist_id}',
          starred_url:
            'https://api.github.com/users/monoclehq/starred{/owner}{/repo}',
          subscriptions_url:
            'https://api.github.com/users/monoclehq/subscriptions',
          organizations_url: 'https://api.github.com/users/monoclehq/orgs',
          repos_url: 'https://api.github.com/users/monoclehq/repos',
          events_url: 'https://api.github.com/users/monoclehq/events{/privacy}',
          received_events_url:
            'https://api.github.com/users/monoclehq/received_events',
          type: 'Organization',
          site_admin: false
        },
        html_url: 'https://github.com/monoclehq/web-manager-dash',
        description: 'Frontend for Manager Dashboard',
        fork: false,
        url: 'https://api.github.com/repos/monoclehq/web-manager-dash',
        forks_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/forks',
        keys_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/keys{/key_id}',
        collaborators_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/collaborators{/collaborator}',
        teams_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/teams',
        hooks_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/hooks',
        issue_events_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/issues/events{/number}',
        events_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/events',
        assignees_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/assignees{/user}',
        branches_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/branches{/branch}',
        tags_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/tags',
        blobs_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/git/blobs{/sha}',
        git_tags_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/git/tags{/sha}',
        git_refs_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/git/refs{/sha}',
        trees_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/git/trees{/sha}',
        statuses_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/statuses/{sha}',
        languages_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/languages',
        stargazers_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/stargazers',
        contributors_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/contributors',
        subscribers_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/subscribers',
        subscription_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/subscription',
        commits_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/commits{/sha}',
        git_commits_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/git/commits{/sha}',
        comments_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/comments{/number}',
        issue_comment_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/issues/comments{/number}',
        contents_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/contents/{+path}',
        compare_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/compare/{base}...{head}',
        merges_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/merges',
        archive_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/{archive_format}{/ref}',
        downloads_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/downloads',
        issues_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/issues{/number}',
        pulls_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/pulls{/number}',
        milestones_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/milestones{/number}',
        notifications_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/notifications{?since,all,participating}',
        labels_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/labels{/name}',
        releases_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/releases{/id}',
        deployments_url:
          'https://api.github.com/repos/monoclehq/web-manager-dash/deployments',
        created_at: '2022-03-30T22:09:18Z',
        updated_at: '2022-04-19T17:40:53Z',
        pushed_at: '2022-05-03T10:11:03Z',
        git_url: 'git://github.com/monoclehq/web-manager-dash.git',
        ssh_url: '**************:monoclehq/web-manager-dash.git',
        clone_url: 'https://github.com/monoclehq/web-manager-dash.git',
        svn_url: 'https://github.com/monoclehq/web-manager-dash',
        homepage: 'https://app.middlewarehq.com',
        size: 4181,
        stargazers_count: 0,
        watchers_count: 0,
        language: 'TypeScript',
        has_issues: true,
        has_projects: true,
        has_downloads: true,
        has_wiki: true,
        has_pages: false,
        forks_count: 0,
        mirror_url: null as null,
        archived: false,
        disabled: false,
        open_issues_count: 1,
        license: null as null,
        allow_forking: false,
        is_template: false,
        topics: [] as const,
        visibility: 'private',
        forks: 0,
        open_issues: 1,
        watchers: 0,
        default_branch: 'main',
        permissions: {
          admin: true,
          maintain: true,
          push: true,
          triage: true,
          pull: true
        }
      },
      {
        id: 476432117,
        node_id: 'R_kgDOHGXG9Q',
        name: 'dum-e',
        full_name: 'monoclehq/dum-e',
        private: true,
        owner: {
          login: 'monoclehq',
          id: 99589860,
          node_id: 'O_kgDOBe-e5A',
          avatar_url: 'https://avatars.githubusercontent.com/u/99589860?v=4',
          gravatar_id: '',
          url: 'https://api.github.com/users/monoclehq',
          html_url: 'https://github.com/monoclehq',
          followers_url: 'https://api.github.com/users/monoclehq/followers',
          following_url:
            'https://api.github.com/users/monoclehq/following{/other_user}',
          gists_url: 'https://api.github.com/users/monoclehq/gists{/gist_id}',
          starred_url:
            'https://api.github.com/users/monoclehq/starred{/owner}{/repo}',
          subscriptions_url:
            'https://api.github.com/users/monoclehq/subscriptions',
          organizations_url: 'https://api.github.com/users/monoclehq/orgs',
          repos_url: 'https://api.github.com/users/monoclehq/repos',
          events_url: 'https://api.github.com/users/monoclehq/events{/privacy}',
          received_events_url:
            'https://api.github.com/users/monoclehq/received_events',
          type: 'Organization',
          site_admin: false
        },
        html_url: 'https://github.com/monoclehq/dum-e',
        description: 'Box it off to the scrapyard.',
        fork: false,
        url: 'https://api.github.com/repos/monoclehq/dum-e',
        forks_url: 'https://api.github.com/repos/monoclehq/dum-e/forks',
        keys_url: 'https://api.github.com/repos/monoclehq/dum-e/keys{/key_id}',
        collaborators_url:
          'https://api.github.com/repos/monoclehq/dum-e/collaborators{/collaborator}',
        teams_url: 'https://api.github.com/repos/monoclehq/dum-e/teams',
        hooks_url: 'https://api.github.com/repos/monoclehq/dum-e/hooks',
        issue_events_url:
          'https://api.github.com/repos/monoclehq/dum-e/issues/events{/number}',
        events_url: 'https://api.github.com/repos/monoclehq/dum-e/events',
        assignees_url:
          'https://api.github.com/repos/monoclehq/dum-e/assignees{/user}',
        branches_url:
          'https://api.github.com/repos/monoclehq/dum-e/branches{/branch}',
        tags_url: 'https://api.github.com/repos/monoclehq/dum-e/tags',
        blobs_url:
          'https://api.github.com/repos/monoclehq/dum-e/git/blobs{/sha}',
        git_tags_url:
          'https://api.github.com/repos/monoclehq/dum-e/git/tags{/sha}',
        git_refs_url:
          'https://api.github.com/repos/monoclehq/dum-e/git/refs{/sha}',
        trees_url:
          'https://api.github.com/repos/monoclehq/dum-e/git/trees{/sha}',
        statuses_url:
          'https://api.github.com/repos/monoclehq/dum-e/statuses/{sha}',
        languages_url: 'https://api.github.com/repos/monoclehq/dum-e/languages',
        stargazers_url:
          'https://api.github.com/repos/monoclehq/dum-e/stargazers',
        contributors_url:
          'https://api.github.com/repos/monoclehq/dum-e/contributors',
        subscribers_url:
          'https://api.github.com/repos/monoclehq/dum-e/subscribers',
        subscription_url:
          'https://api.github.com/repos/monoclehq/dum-e/subscription',
        commits_url:
          'https://api.github.com/repos/monoclehq/dum-e/commits{/sha}',
        git_commits_url:
          'https://api.github.com/repos/monoclehq/dum-e/git/commits{/sha}',
        comments_url:
          'https://api.github.com/repos/monoclehq/dum-e/comments{/number}',
        issue_comment_url:
          'https://api.github.com/repos/monoclehq/dum-e/issues/comments{/number}',
        contents_url:
          'https://api.github.com/repos/monoclehq/dum-e/contents/{+path}',
        compare_url:
          'https://api.github.com/repos/monoclehq/dum-e/compare/{base}...{head}',
        merges_url: 'https://api.github.com/repos/monoclehq/dum-e/merges',
        archive_url:
          'https://api.github.com/repos/monoclehq/dum-e/{archive_format}{/ref}',
        downloads_url: 'https://api.github.com/repos/monoclehq/dum-e/downloads',
        issues_url:
          'https://api.github.com/repos/monoclehq/dum-e/issues{/number}',
        pulls_url:
          'https://api.github.com/repos/monoclehq/dum-e/pulls{/number}',
        milestones_url:
          'https://api.github.com/repos/monoclehq/dum-e/milestones{/number}',
        notifications_url:
          'https://api.github.com/repos/monoclehq/dum-e/notifications{?since,all,participating}',
        labels_url:
          'https://api.github.com/repos/monoclehq/dum-e/labels{/name}',
        releases_url:
          'https://api.github.com/repos/monoclehq/dum-e/releases{/id}',
        deployments_url:
          'https://api.github.com/repos/monoclehq/dum-e/deployments',
        created_at: '2022-03-31T18:32:37Z',
        updated_at: '2022-03-31T18:32:37Z',
        pushed_at: '2022-03-31T18:37:53Z',
        git_url: 'git://github.com/monoclehq/dum-e.git',
        ssh_url: '**************:monoclehq/dum-e.git',
        clone_url: 'https://github.com/monoclehq/dum-e.git',
        svn_url: 'https://github.com/monoclehq/dum-e',
        homepage: null as null,
        size: 1,
        stargazers_count: 0,
        watchers_count: 0,
        language: null as null,
        has_issues: true,
        has_projects: true,
        has_downloads: true,
        has_wiki: true,
        has_pages: false,
        forks_count: 0,
        mirror_url: null as null,
        archived: false,
        disabled: false,
        open_issues_count: 0,
        license: null as null,
        allow_forking: false,
        is_template: false,
        topics: [] as const,
        visibility: 'private',
        forks: 0,
        open_issues: 0,
        watchers: 0,
        default_branch: 'main',
        permissions: {
          admin: true,
          maintain: true,
          push: true,
          triage: true,
          pull: true
        }
      },
      {
        id: 487635948,
        node_id: 'R_kgDOHRC77A',
        name: 'legal',
        full_name: 'monoclehq/legal',
        private: true,
        owner: {
          login: 'monoclehq',
          id: 99589860,
          node_id: 'O_kgDOBe-e5A',
          avatar_url: 'https://avatars.githubusercontent.com/u/99589860?v=4',
          gravatar_id: '',
          url: 'https://api.github.com/users/monoclehq',
          html_url: 'https://github.com/monoclehq',
          followers_url: 'https://api.github.com/users/monoclehq/followers',
          following_url:
            'https://api.github.com/users/monoclehq/following{/other_user}',
          gists_url: 'https://api.github.com/users/monoclehq/gists{/gist_id}',
          starred_url:
            'https://api.github.com/users/monoclehq/starred{/owner}{/repo}',
          subscriptions_url:
            'https://api.github.com/users/monoclehq/subscriptions',
          organizations_url: 'https://api.github.com/users/monoclehq/orgs',
          repos_url: 'https://api.github.com/users/monoclehq/repos',
          events_url: 'https://api.github.com/users/monoclehq/events{/privacy}',
          received_events_url:
            'https://api.github.com/users/monoclehq/received_events',
          type: 'Organization',
          site_admin: false
        },
        html_url: 'https://github.com/monoclehq/legal',
        description: null,
        fork: false,
        url: 'https://api.github.com/repos/monoclehq/legal',
        forks_url: 'https://api.github.com/repos/monoclehq/legal/forks',
        keys_url: 'https://api.github.com/repos/monoclehq/legal/keys{/key_id}',
        collaborators_url:
          'https://api.github.com/repos/monoclehq/legal/collaborators{/collaborator}',
        teams_url: 'https://api.github.com/repos/monoclehq/legal/teams',
        hooks_url: 'https://api.github.com/repos/monoclehq/legal/hooks',
        issue_events_url:
          'https://api.github.com/repos/monoclehq/legal/issues/events{/number}',
        events_url: 'https://api.github.com/repos/monoclehq/legal/events',
        assignees_url:
          'https://api.github.com/repos/monoclehq/legal/assignees{/user}',
        branches_url:
          'https://api.github.com/repos/monoclehq/legal/branches{/branch}',
        tags_url: 'https://api.github.com/repos/monoclehq/legal/tags',
        blobs_url:
          'https://api.github.com/repos/monoclehq/legal/git/blobs{/sha}',
        git_tags_url:
          'https://api.github.com/repos/monoclehq/legal/git/tags{/sha}',
        git_refs_url:
          'https://api.github.com/repos/monoclehq/legal/git/refs{/sha}',
        trees_url:
          'https://api.github.com/repos/monoclehq/legal/git/trees{/sha}',
        statuses_url:
          'https://api.github.com/repos/monoclehq/legal/statuses/{sha}',
        languages_url: 'https://api.github.com/repos/monoclehq/legal/languages',
        stargazers_url:
          'https://api.github.com/repos/monoclehq/legal/stargazers',
        contributors_url:
          'https://api.github.com/repos/monoclehq/legal/contributors',
        subscribers_url:
          'https://api.github.com/repos/monoclehq/legal/subscribers',
        subscription_url:
          'https://api.github.com/repos/monoclehq/legal/subscription',
        commits_url:
          'https://api.github.com/repos/monoclehq/legal/commits{/sha}',
        git_commits_url:
          'https://api.github.com/repos/monoclehq/legal/git/commits{/sha}',
        comments_url:
          'https://api.github.com/repos/monoclehq/legal/comments{/number}',
        issue_comment_url:
          'https://api.github.com/repos/monoclehq/legal/issues/comments{/number}',
        contents_url:
          'https://api.github.com/repos/monoclehq/legal/contents/{+path}',
        compare_url:
          'https://api.github.com/repos/monoclehq/legal/compare/{base}...{head}',
        merges_url: 'https://api.github.com/repos/monoclehq/legal/merges',
        archive_url:
          'https://api.github.com/repos/monoclehq/legal/{archive_format}{/ref}',
        downloads_url: 'https://api.github.com/repos/monoclehq/legal/downloads',
        issues_url:
          'https://api.github.com/repos/monoclehq/legal/issues{/number}',
        pulls_url:
          'https://api.github.com/repos/monoclehq/legal/pulls{/number}',
        milestones_url:
          'https://api.github.com/repos/monoclehq/legal/milestones{/number}',
        notifications_url:
          'https://api.github.com/repos/monoclehq/legal/notifications{?since,all,participating}',
        labels_url:
          'https://api.github.com/repos/monoclehq/legal/labels{/name}',
        releases_url:
          'https://api.github.com/repos/monoclehq/legal/releases{/id}',
        deployments_url:
          'https://api.github.com/repos/monoclehq/legal/deployments',
        created_at: '2022-05-01T20:29:04Z',
        updated_at: '2022-05-01T20:29:04Z',
        pushed_at: '2022-05-01T20:41:56Z',
        git_url: 'git://github.com/monoclehq/legal.git',
        ssh_url: '**************:monoclehq/legal.git',
        clone_url: 'https://github.com/monoclehq/legal.git',
        svn_url: 'https://github.com/monoclehq/legal',
        homepage: null as null,
        size: 14,
        stargazers_count: 0,
        watchers_count: 0,
        language: null as null,
        has_issues: true,
        has_projects: true,
        has_downloads: true,
        has_wiki: true,
        has_pages: false,
        forks_count: 0,
        mirror_url: null as null,
        archived: false,
        disabled: false,
        open_issues_count: 0,
        license: null as null,
        allow_forking: false,
        is_template: false,
        topics: [] as const,
        visibility: 'private',
        forks: 0,
        open_issues: 0,
        watchers: 0,
        default_branch: 'main',
        permissions: {
          admin: true,
          maintain: true,
          push: true,
          triage: true,
          pull: true
        }
      }
    ]
  }
];

export const selectedDBReposMock = [
  {
    org_id: '23d9e173-e98d-4ffd-b025-b5e7dbf0962f',
    name: 'web-manager-dash',
    provider: 'github',
    created_at: '2022-04-15T16:26:40.855853+00:00',
    updated_at: '2022-05-02T16:06:39.386+00:00',
    id: '328d4e5d-ae5d-45f9-9818-66f56110a3a9',
    org_name: 'monoclehq',
    is_active: true
  },
  {
    org_id: '23d9e173-e98d-4ffd-b025-b5e7dbf0962f',
    name: 'monorepo',
    provider: 'github',
    created_at: '2022-04-15T16:26:40.855853+00:00',
    updated_at: '2022-05-02T16:06:39.386+00:00',
    id: '5b79d8e1-7133-48dc-876d-0670495800c2',
    org_name: 'monoclehq',
    is_active: true
  }
];

export const selectedGHReposMock = [
  {
    id: 458772068,
    node_id: 'R_kgDOG1hOZA',
    name: 'monorepo',
    full_name: 'monoclehq/monorepo',
    private: true,
    owner: {
      login: 'monoclehq',
      id: 99589860,
      node_id: 'O_kgDOBe-e5A',
      avatar_url: 'https://avatars.githubusercontent.com/u/99589860?v=4',
      gravatar_id: '',
      url: 'https://api.github.com/users/monoclehq',
      html_url: 'https://github.com/monoclehq',
      followers_url: 'https://api.github.com/users/monoclehq/followers',
      following_url:
        'https://api.github.com/users/monoclehq/following{/other_user}',
      gists_url: 'https://api.github.com/users/monoclehq/gists{/gist_id}',
      starred_url:
        'https://api.github.com/users/monoclehq/starred{/owner}{/repo}',
      subscriptions_url: 'https://api.github.com/users/monoclehq/subscriptions',
      organizations_url: 'https://api.github.com/users/monoclehq/orgs',
      repos_url: 'https://api.github.com/users/monoclehq/repos',
      events_url: 'https://api.github.com/users/monoclehq/events{/privacy}',
      received_events_url:
        'https://api.github.com/users/monoclehq/received_events',
      type: 'Organization',
      site_admin: false
    },
    html_url: 'https://github.com/monoclehq/monorepo',
    description: null as null,
    fork: false,
    url: 'https://api.github.com/repos/monoclehq/monorepo',
    forks_url: 'https://api.github.com/repos/monoclehq/monorepo/forks',
    keys_url: 'https://api.github.com/repos/monoclehq/monorepo/keys{/key_id}',
    collaborators_url:
      'https://api.github.com/repos/monoclehq/monorepo/collaborators{/collaborator}',
    teams_url: 'https://api.github.com/repos/monoclehq/monorepo/teams',
    hooks_url: 'https://api.github.com/repos/monoclehq/monorepo/hooks',
    issue_events_url:
      'https://api.github.com/repos/monoclehq/monorepo/issues/events{/number}',
    events_url: 'https://api.github.com/repos/monoclehq/monorepo/events',
    assignees_url:
      'https://api.github.com/repos/monoclehq/monorepo/assignees{/user}',
    branches_url:
      'https://api.github.com/repos/monoclehq/monorepo/branches{/branch}',
    tags_url: 'https://api.github.com/repos/monoclehq/monorepo/tags',
    blobs_url:
      'https://api.github.com/repos/monoclehq/monorepo/git/blobs{/sha}',
    git_tags_url:
      'https://api.github.com/repos/monoclehq/monorepo/git/tags{/sha}',
    git_refs_url:
      'https://api.github.com/repos/monoclehq/monorepo/git/refs{/sha}',
    trees_url:
      'https://api.github.com/repos/monoclehq/monorepo/git/trees{/sha}',
    statuses_url:
      'https://api.github.com/repos/monoclehq/monorepo/statuses/{sha}',
    languages_url: 'https://api.github.com/repos/monoclehq/monorepo/languages',
    stargazers_url:
      'https://api.github.com/repos/monoclehq/monorepo/stargazers',
    contributors_url:
      'https://api.github.com/repos/monoclehq/monorepo/contributors',
    subscribers_url:
      'https://api.github.com/repos/monoclehq/monorepo/subscribers',
    subscription_url:
      'https://api.github.com/repos/monoclehq/monorepo/subscription',
    commits_url:
      'https://api.github.com/repos/monoclehq/monorepo/commits{/sha}',
    git_commits_url:
      'https://api.github.com/repos/monoclehq/monorepo/git/commits{/sha}',
    comments_url:
      'https://api.github.com/repos/monoclehq/monorepo/comments{/number}',
    issue_comment_url:
      'https://api.github.com/repos/monoclehq/monorepo/issues/comments{/number}',
    contents_url:
      'https://api.github.com/repos/monoclehq/monorepo/contents/{+path}',
    compare_url:
      'https://api.github.com/repos/monoclehq/monorepo/compare/{base}...{head}',
    merges_url: 'https://api.github.com/repos/monoclehq/monorepo/merges',
    archive_url:
      'https://api.github.com/repos/monoclehq/monorepo/{archive_format}{/ref}',
    downloads_url: 'https://api.github.com/repos/monoclehq/monorepo/downloads',
    issues_url:
      'https://api.github.com/repos/monoclehq/monorepo/issues{/number}',
    pulls_url: 'https://api.github.com/repos/monoclehq/monorepo/pulls{/number}',
    milestones_url:
      'https://api.github.com/repos/monoclehq/monorepo/milestones{/number}',
    notifications_url:
      'https://api.github.com/repos/monoclehq/monorepo/notifications{?since,all,participating}',
    labels_url: 'https://api.github.com/repos/monoclehq/monorepo/labels{/name}',
    releases_url:
      'https://api.github.com/repos/monoclehq/monorepo/releases{/id}',
    deployments_url:
      'https://api.github.com/repos/monoclehq/monorepo/deployments',
    created_at: '2022-02-13T10:09:16Z',
    updated_at: '2022-03-20T12:16:19Z',
    pushed_at: '2022-05-03T07:18:16Z',
    git_url: 'git://github.com/monoclehq/monorepo.git',
    ssh_url: '**************:monoclehq/monorepo.git',
    clone_url: 'https://github.com/monoclehq/monorepo.git',
    svn_url: 'https://github.com/monoclehq/monorepo',
    homepage: null as null,
    size: 166,
    stargazers_count: 0,
    watchers_count: 0,
    language: 'Python',
    has_issues: true,
    has_projects: true,
    has_downloads: true,
    has_wiki: true,
    has_pages: false,
    forks_count: 0,
    mirror_url: null as null,
    archived: false,
    disabled: false,
    open_issues_count: 0,
    license: null as null,
    allow_forking: false,
    is_template: false,
    topics: [] as const,
    visibility: 'private',
    forks: 0,
    open_issues: 0,
    watchers: 0,
    default_branch: 'master',
    permissions: {
      admin: true,
      maintain: true,
      push: true,
      triage: true,
      pull: true
    }
  },
  {
    id: 476066666,
    node_id: 'R_kgDOHGAzag',
    name: 'web-manager-dash',
    full_name: 'monoclehq/web-manager-dash',
    private: true,
    owner: {
      login: 'monoclehq',
      id: 99589860,
      node_id: 'O_kgDOBe-e5A',
      avatar_url: 'https://avatars.githubusercontent.com/u/99589860?v=4',
      gravatar_id: '',
      url: 'https://api.github.com/users/monoclehq',
      html_url: 'https://github.com/monoclehq',
      followers_url: 'https://api.github.com/users/monoclehq/followers',
      following_url:
        'https://api.github.com/users/monoclehq/following{/other_user}',
      gists_url: 'https://api.github.com/users/monoclehq/gists{/gist_id}',
      starred_url:
        'https://api.github.com/users/monoclehq/starred{/owner}{/repo}',
      subscriptions_url: 'https://api.github.com/users/monoclehq/subscriptions',
      organizations_url: 'https://api.github.com/users/monoclehq/orgs',
      repos_url: 'https://api.github.com/users/monoclehq/repos',
      events_url: 'https://api.github.com/users/monoclehq/events{/privacy}',
      received_events_url:
        'https://api.github.com/users/monoclehq/received_events',
      type: 'Organization',
      site_admin: false
    },
    html_url: 'https://github.com/monoclehq/web-manager-dash',
    description: 'Frontend for Manager Dashboard',
    fork: false,
    url: 'https://api.github.com/repos/monoclehq/web-manager-dash',
    forks_url: 'https://api.github.com/repos/monoclehq/web-manager-dash/forks',
    keys_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/keys{/key_id}',
    collaborators_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/collaborators{/collaborator}',
    teams_url: 'https://api.github.com/repos/monoclehq/web-manager-dash/teams',
    hooks_url: 'https://api.github.com/repos/monoclehq/web-manager-dash/hooks',
    issue_events_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/issues/events{/number}',
    events_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/events',
    assignees_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/assignees{/user}',
    branches_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/branches{/branch}',
    tags_url: 'https://api.github.com/repos/monoclehq/web-manager-dash/tags',
    blobs_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/git/blobs{/sha}',
    git_tags_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/git/tags{/sha}',
    git_refs_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/git/refs{/sha}',
    trees_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/git/trees{/sha}',
    statuses_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/statuses/{sha}',
    languages_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/languages',
    stargazers_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/stargazers',
    contributors_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/contributors',
    subscribers_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/subscribers',
    subscription_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/subscription',
    commits_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/commits{/sha}',
    git_commits_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/git/commits{/sha}',
    comments_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/comments{/number}',
    issue_comment_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/issues/comments{/number}',
    contents_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/contents/{+path}',
    compare_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/compare/{base}...{head}',
    merges_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/merges',
    archive_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/{archive_format}{/ref}',
    downloads_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/downloads',
    issues_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/issues{/number}',
    pulls_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/pulls{/number}',
    milestones_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/milestones{/number}',
    notifications_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/notifications{?since,all,participating}',
    labels_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/labels{/name}',
    releases_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/releases{/id}',
    deployments_url:
      'https://api.github.com/repos/monoclehq/web-manager-dash/deployments',
    created_at: '2022-03-30T22:09:18Z',
    updated_at: '2022-04-19T17:40:53Z',
    pushed_at: '2022-05-03T10:11:03Z',
    git_url: 'git://github.com/monoclehq/web-manager-dash.git',
    ssh_url: '**************:monoclehq/web-manager-dash.git',
    clone_url: 'https://github.com/monoclehq/web-manager-dash.git',
    svn_url: 'https://github.com/monoclehq/web-manager-dash',
    homepage: 'https://app.middlewarehq.com',
    size: 4181,
    stargazers_count: 0,
    watchers_count: 0,
    language: 'TypeScript',
    has_issues: true,
    has_projects: true,
    has_downloads: true,
    has_wiki: true,
    has_pages: false,
    forks_count: 0,
    mirror_url: null as null,
    archived: false,
    disabled: false,
    open_issues_count: 1,
    license: null as null,
    allow_forking: false,
    is_template: false,
    topics: [] as const,
    visibility: 'private',
    forks: 0,
    open_issues: 1,
    watchers: 0,
    default_branch: 'main',
    permissions: {
      admin: true,
      maintain: true,
      push: true,
      triage: true,
      pull: true
    }
  },
  {
    id: 476432117,
    node_id: 'R_kgDOHGXG9Q',
    name: 'dum-e',
    full_name: 'monoclehq/dum-e',
    private: true,
    owner: {
      login: 'monoclehq',
      id: 99589860,
      node_id: 'O_kgDOBe-e5A',
      avatar_url: 'https://avatars.githubusercontent.com/u/99589860?v=4',
      gravatar_id: '',
      url: 'https://api.github.com/users/monoclehq',
      html_url: 'https://github.com/monoclehq',
      followers_url: 'https://api.github.com/users/monoclehq/followers',
      following_url:
        'https://api.github.com/users/monoclehq/following{/other_user}',
      gists_url: 'https://api.github.com/users/monoclehq/gists{/gist_id}',
      starred_url:
        'https://api.github.com/users/monoclehq/starred{/owner}{/repo}',
      subscriptions_url: 'https://api.github.com/users/monoclehq/subscriptions',
      organizations_url: 'https://api.github.com/users/monoclehq/orgs',
      repos_url: 'https://api.github.com/users/monoclehq/repos',
      events_url: 'https://api.github.com/users/monoclehq/events{/privacy}',
      received_events_url:
        'https://api.github.com/users/monoclehq/received_events',
      type: 'Organization',
      site_admin: false
    },
    html_url: 'https://github.com/monoclehq/dum-e',
    description: 'Box it off to the scrapyard.',
    fork: false,
    url: 'https://api.github.com/repos/monoclehq/dum-e',
    forks_url: 'https://api.github.com/repos/monoclehq/dum-e/forks',
    keys_url: 'https://api.github.com/repos/monoclehq/dum-e/keys{/key_id}',
    collaborators_url:
      'https://api.github.com/repos/monoclehq/dum-e/collaborators{/collaborator}',
    teams_url: 'https://api.github.com/repos/monoclehq/dum-e/teams',
    hooks_url: 'https://api.github.com/repos/monoclehq/dum-e/hooks',
    issue_events_url:
      'https://api.github.com/repos/monoclehq/dum-e/issues/events{/number}',
    events_url: 'https://api.github.com/repos/monoclehq/dum-e/events',
    assignees_url:
      'https://api.github.com/repos/monoclehq/dum-e/assignees{/user}',
    branches_url:
      'https://api.github.com/repos/monoclehq/dum-e/branches{/branch}',
    tags_url: 'https://api.github.com/repos/monoclehq/dum-e/tags',
    blobs_url: 'https://api.github.com/repos/monoclehq/dum-e/git/blobs{/sha}',
    git_tags_url: 'https://api.github.com/repos/monoclehq/dum-e/git/tags{/sha}',
    git_refs_url: 'https://api.github.com/repos/monoclehq/dum-e/git/refs{/sha}',
    trees_url: 'https://api.github.com/repos/monoclehq/dum-e/git/trees{/sha}',
    statuses_url: 'https://api.github.com/repos/monoclehq/dum-e/statuses/{sha}',
    languages_url: 'https://api.github.com/repos/monoclehq/dum-e/languages',
    stargazers_url: 'https://api.github.com/repos/monoclehq/dum-e/stargazers',
    contributors_url:
      'https://api.github.com/repos/monoclehq/dum-e/contributors',
    subscribers_url: 'https://api.github.com/repos/monoclehq/dum-e/subscribers',
    subscription_url:
      'https://api.github.com/repos/monoclehq/dum-e/subscription',
    commits_url: 'https://api.github.com/repos/monoclehq/dum-e/commits{/sha}',
    git_commits_url:
      'https://api.github.com/repos/monoclehq/dum-e/git/commits{/sha}',
    comments_url:
      'https://api.github.com/repos/monoclehq/dum-e/comments{/number}',
    issue_comment_url:
      'https://api.github.com/repos/monoclehq/dum-e/issues/comments{/number}',
    contents_url:
      'https://api.github.com/repos/monoclehq/dum-e/contents/{+path}',
    compare_url:
      'https://api.github.com/repos/monoclehq/dum-e/compare/{base}...{head}',
    merges_url: 'https://api.github.com/repos/monoclehq/dum-e/merges',
    archive_url:
      'https://api.github.com/repos/monoclehq/dum-e/{archive_format}{/ref}',
    downloads_url: 'https://api.github.com/repos/monoclehq/dum-e/downloads',
    issues_url: 'https://api.github.com/repos/monoclehq/dum-e/issues{/number}',
    pulls_url: 'https://api.github.com/repos/monoclehq/dum-e/pulls{/number}',
    milestones_url:
      'https://api.github.com/repos/monoclehq/dum-e/milestones{/number}',
    notifications_url:
      'https://api.github.com/repos/monoclehq/dum-e/notifications{?since,all,participating}',
    labels_url: 'https://api.github.com/repos/monoclehq/dum-e/labels{/name}',
    releases_url: 'https://api.github.com/repos/monoclehq/dum-e/releases{/id}',
    deployments_url: 'https://api.github.com/repos/monoclehq/dum-e/deployments',
    created_at: '2022-03-31T18:32:37Z',
    updated_at: '2022-03-31T18:32:37Z',
    pushed_at: '2022-03-31T18:37:53Z',
    git_url: 'git://github.com/monoclehq/dum-e.git',
    ssh_url: '**************:monoclehq/dum-e.git',
    clone_url: 'https://github.com/monoclehq/dum-e.git',
    svn_url: 'https://github.com/monoclehq/dum-e',
    homepage: null as null,
    size: 1,
    stargazers_count: 0,
    watchers_count: 0,
    language: null as null,
    has_issues: true,
    has_projects: true,
    has_downloads: true,
    has_wiki: true,
    has_pages: false,
    forks_count: 0,
    mirror_url: null as null,
    archived: false,
    disabled: false,
    open_issues_count: 0,
    license: null as null,
    allow_forking: false,
    is_template: false,
    topics: [] as const,
    visibility: 'private',
    forks: 0,
    open_issues: 0,
    watchers: 0,
    default_branch: 'main',
    permissions: {
      admin: true,
      maintain: true,
      push: true,
      triage: true,
      pull: true
    }
  },
  {
    id: 487635948,
    node_id: 'R_kgDOHRC77A',
    name: 'legal',
    full_name: 'monoclehq/legal',
    private: true,
    owner: {
      login: 'monoclehq',
      id: 99589860,
      node_id: 'O_kgDOBe-e5A',
      avatar_url: 'https://avatars.githubusercontent.com/u/99589860?v=4',
      gravatar_id: '',
      url: 'https://api.github.com/users/monoclehq',
      html_url: 'https://github.com/monoclehq',
      followers_url: 'https://api.github.com/users/monoclehq/followers',
      following_url:
        'https://api.github.com/users/monoclehq/following{/other_user}',
      gists_url: 'https://api.github.com/users/monoclehq/gists{/gist_id}',
      starred_url:
        'https://api.github.com/users/monoclehq/starred{/owner}{/repo}',
      subscriptions_url: 'https://api.github.com/users/monoclehq/subscriptions',
      organizations_url: 'https://api.github.com/users/monoclehq/orgs',
      repos_url: 'https://api.github.com/users/monoclehq/repos',
      events_url: 'https://api.github.com/users/monoclehq/events{/privacy}',
      received_events_url:
        'https://api.github.com/users/monoclehq/received_events',
      type: 'Organization',
      site_admin: false
    },
    html_url: 'https://github.com/monoclehq/legal',
    description: null as null,
    fork: false,
    url: 'https://api.github.com/repos/monoclehq/legal',
    forks_url: 'https://api.github.com/repos/monoclehq/legal/forks',
    keys_url: 'https://api.github.com/repos/monoclehq/legal/keys{/key_id}',
    collaborators_url:
      'https://api.github.com/repos/monoclehq/legal/collaborators{/collaborator}',
    teams_url: 'https://api.github.com/repos/monoclehq/legal/teams',
    hooks_url: 'https://api.github.com/repos/monoclehq/legal/hooks',
    issue_events_url:
      'https://api.github.com/repos/monoclehq/legal/issues/events{/number}',
    events_url: 'https://api.github.com/repos/monoclehq/legal/events',
    assignees_url:
      'https://api.github.com/repos/monoclehq/legal/assignees{/user}',
    branches_url:
      'https://api.github.com/repos/monoclehq/legal/branches{/branch}',
    tags_url: 'https://api.github.com/repos/monoclehq/legal/tags',
    blobs_url: 'https://api.github.com/repos/monoclehq/legal/git/blobs{/sha}',
    git_tags_url: 'https://api.github.com/repos/monoclehq/legal/git/tags{/sha}',
    git_refs_url: 'https://api.github.com/repos/monoclehq/legal/git/refs{/sha}',
    trees_url: 'https://api.github.com/repos/monoclehq/legal/git/trees{/sha}',
    statuses_url: 'https://api.github.com/repos/monoclehq/legal/statuses/{sha}',
    languages_url: 'https://api.github.com/repos/monoclehq/legal/languages',
    stargazers_url: 'https://api.github.com/repos/monoclehq/legal/stargazers',
    contributors_url:
      'https://api.github.com/repos/monoclehq/legal/contributors',
    subscribers_url: 'https://api.github.com/repos/monoclehq/legal/subscribers',
    subscription_url:
      'https://api.github.com/repos/monoclehq/legal/subscription',
    commits_url: 'https://api.github.com/repos/monoclehq/legal/commits{/sha}',
    git_commits_url:
      'https://api.github.com/repos/monoclehq/legal/git/commits{/sha}',
    comments_url:
      'https://api.github.com/repos/monoclehq/legal/comments{/number}',
    issue_comment_url:
      'https://api.github.com/repos/monoclehq/legal/issues/comments{/number}',
    contents_url:
      'https://api.github.com/repos/monoclehq/legal/contents/{+path}',
    compare_url:
      'https://api.github.com/repos/monoclehq/legal/compare/{base}...{head}',
    merges_url: 'https://api.github.com/repos/monoclehq/legal/merges',
    archive_url:
      'https://api.github.com/repos/monoclehq/legal/{archive_format}{/ref}',
    downloads_url: 'https://api.github.com/repos/monoclehq/legal/downloads',
    issues_url: 'https://api.github.com/repos/monoclehq/legal/issues{/number}',
    pulls_url: 'https://api.github.com/repos/monoclehq/legal/pulls{/number}',
    milestones_url:
      'https://api.github.com/repos/monoclehq/legal/milestones{/number}',
    notifications_url:
      'https://api.github.com/repos/monoclehq/legal/notifications{?since,all,participating}',
    labels_url: 'https://api.github.com/repos/monoclehq/legal/labels{/name}',
    releases_url: 'https://api.github.com/repos/monoclehq/legal/releases{/id}',
    deployments_url: 'https://api.github.com/repos/monoclehq/legal/deployments',
    created_at: '2022-05-01T20:29:04Z',
    updated_at: '2022-05-01T20:29:04Z',
    pushed_at: '2022-05-01T20:41:56Z',
    git_url: 'git://github.com/monoclehq/legal.git',
    ssh_url: '**************:monoclehq/legal.git',
    clone_url: 'https://github.com/monoclehq/legal.git',
    svn_url: 'https://github.com/monoclehq/legal',
    homepage: null as null,
    size: 14,
    stargazers_count: 0,
    watchers_count: 0,
    language: null as null,
    has_issues: true,
    has_projects: true,
    has_downloads: true,
    has_wiki: true,
    has_pages: false,
    forks_count: 0,
    mirror_url: null as null,
    archived: false,
    disabled: false,
    open_issues_count: 0,
    license: null as null,
    allow_forking: false,
    is_template: false,
    topics: [] as const,
    visibility: 'private',
    forks: 0,
    open_issues: 0,
    watchers: 0,
    default_branch: 'main',
    permissions: {
      admin: true,
      maintain: true,
      push: true,
      triage: true,
      pull: true
    }
  }
];
