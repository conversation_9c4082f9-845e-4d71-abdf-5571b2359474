import faker from '@faker-js/faker';

export const mockResolvedIncidents = [
  {
    id: '27a05756-d541-43f7-827d-bddb45ce86b2',
    title: '',
    key: 'XeiHmMsBBaCMtf32HaAzUb',
    incident_number: 38865,
    provider: 'zenduty',
    status: 'resolved',
    service_id: '612468bc-c1ab-4ac8-b043-abfd5af05ed4',
    creation_date: '2023-07-05T13:34:38.296083+00:00',
    resolved_date: '2023-07-05T13:39:36.880121+00:00',
    acknowledged_date: '2023-07-05T13:39:36.880121+00:00',
    assigned_to: {
      username: 'jayantbh',
      linked_user: {
        id: 'febe49f3-5009-433c-bbd1-08c4db9caf67',
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/jayantbh.png'
      }
    },
    url: '/#',
    summary: ''
  },
  {
    id: '4974c38e-0d6d-42d0-82b8-04fc2e76ffca',
    title: '',
    key: 'YaiJxipeEXBon6KYpt5eCg',
    incident_number: 38885,
    provider: 'zenduty',
    status: 'resolved',
    service_id: '612468bc-c1ab-4ac8-b043-abfd5af05ed4',
    creation_date: '2023-07-06T04:29:45.454630+00:00',
    resolved_date: '2023-07-06T04:35:45.982214+00:00',
    acknowledged_date: '2023-07-06T04:30:17.776824+00:00',
    assigned_to: {
      username: 'jayantbh',
      linked_user: {
        id: 'febe49f3-5009-433c-bbd1-08c4db9caf67',
        name: 'Jayant Bhawal',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/jayantbh.png'
      }
    },
    url: '/#',
    summary: ''
  },
  {
    id: '20252a15-d49c-4d78-8a66-a7bfd8f48e5a',
    title: '',
    key: '5NpaG7p4LeMvMrnRMfZP3V',
    incident_number: 39079,
    provider: 'zenduty',
    status: 'resolved',
    service_id: '612468bc-c1ab-4ac8-b043-abfd5af05ed4',
    creation_date: '2023-07-11T04:21:23.718481+00:00',
    resolved_date: '2023-07-11T04:25:23.389307+00:00',
    acknowledged_date: '2023-07-11T04:25:23.389307+00:00',
    assigned_to: {
      username: 'jayantbh',
      linked_user: {
        id: 'febe49f3-5009-433c-bbd1-08c4db9caf67',
        name: 'Jayant Bhawal',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/jayantbh.png'
      }
    },
    url: '/#',
    summary: ''
  },
  {
    id: 'd9e360dc-b860-45a0-be43-1f581442aa48',
    title: '',
    key: 'dUsaeHaUDjTwTwB2BLBHG5',
    incident_number: 39250,
    provider: 'zenduty',
    status: 'resolved',
    service_id: '612468bc-c1ab-4ac8-b043-abfd5af05ed4',
    creation_date: '2023-07-16T00:32:02.121820+00:00',
    resolved_date: '2023-07-16T02:57:05.897997+00:00',
    acknowledged_date: '2023-07-16T00:47:28.315189+00:00',
    assigned_to: {
      username: 'jayantbh',
      linked_user: {
        id: '042c848b-1c1e-4557-aa5c-f5e95feb9dd9',
        name: 'Jayant Bhawal',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/jayantbh.png'
      }
    },
    url: '/#',
    summary: ''
  },
  {
    id: 'b300f0bf-42d5-41e1-9d48-7e57a055179d',
    title: '',
    key: 'dcxLK86N46DfkQ86NDtDSK',
    incident_number: 39338,
    provider: 'zenduty',
    status: 'resolved',
    service_id: '612468bc-c1ab-4ac8-b043-abfd5af05ed4',
    creation_date: '2023-07-19T04:34:23.538263+00:00',
    resolved_date: '2023-07-19T04:38:23.165648+00:00',
    acknowledged_date: '2023-07-19T04:36:38.019357+00:00',
    assigned_to: {
      username: 'jayantbh',
      linked_user: {
        id: '042c848b-1c1e-4557-aa5c-f5e95feb9dd9',
        name: 'Jayant Bhawal',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/jayantbh.png'
      }
    },
    url: '/#',
    summary: ''
  },
  {
    id: '34027995-a55f-43f6-a38d-3e89cf32b3d0',
    title: '',
    key: '84dJgyoqJDZoSMEtEWuADH',
    incident_number: 39521,
    provider: 'zenduty',
    status: 'resolved',
    service_id: '612468bc-c1ab-4ac8-b043-abfd5af05ed4',
    creation_date: '2023-07-27T09:13:42.521983+00:00',
    resolved_date: '2023-07-27T10:42:41.655814+00:00',
    acknowledged_date: '2023-07-27T09:24:31.392397+00:00',
    assigned_to: {
      username: 'jayantbh',
      linked_user: {
        id: '20a15d33-a810-43f8-9c2f-2cdf9e0907cb',
        name: 'Jayant Bhawal',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/jayantbh.png'
      }
    },
    url: '/#',
    summary: ''
  },
  {
    id: '20a57174-e599-4b6d-bc57-95117e6c3365',
    title: '',
    key: 'fcTwtVULME5v6wKpMgcVNQ',
    incident_number: 39546,
    provider: 'zenduty',
    status: 'resolved',
    service_id: '612468bc-c1ab-4ac8-b043-abfd5af05ed4',
    creation_date: '2023-07-28T05:06:16.865388+00:00',
    resolved_date: '2023-07-28T14:07:16.517315+00:00',
    acknowledged_date: '2023-07-28T05:06:36.443115+00:00',
    assigned_to: {
      username: 'jayantbh',
      linked_user: {
        id: '20a15d33-a810-43f8-9c2f-2cdf9e0907cb',
        name: 'Jayant Bhawal',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/jayantbh.png'
      }
    },
    url: '/#',
    summary: ''
  },
  {
    id: '730fbeb0-5987-4afb-8313-541d2fa5540d',
    title: '',
    key: 'FeWsLD8uALQwPQdRm9oGJB',
    incident_number: 39549,
    provider: 'zenduty',
    status: 'resolved',
    service_id: '612468bc-c1ab-4ac8-b043-abfd5af05ed4',
    creation_date: '2023-07-28T07:09:53.189984+00:00',
    resolved_date: '2023-07-28T09:09:53.137975+00:00',
    acknowledged_date: '2023-07-28T07:10:35.012727+00:00',
    assigned_to: {
      username: 'jayantbh',
      linked_user: {
        id: '20a15d33-a810-43f8-9c2f-2cdf9e0907cb',
        name: 'Jayant Bhawal',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/jayantbh.png'
      }
    },
    url: '/#',
    summary: ''
  },
  {
    id: '22a95e0d-10d4-4997-af0f-4d9f607b0fc2',
    title: '',
    key: 'NhF49GAZDWkksimkGgSqNC',
    incident_number: 39609,
    provider: 'zenduty',
    status: 'resolved',
    service_id: '612468bc-c1ab-4ac8-b043-abfd5af05ed4',
    creation_date: '2023-07-31T16:48:38.316891+00:00',
    resolved_date: '2023-07-31T17:04:37.778094+00:00',
    acknowledged_date: '2023-07-31T17:04:01.462369+00:00',
    assigned_to: {
      username: 'jayantbh',
      linked_user: {
        id: '3cc26fa0-c9d4-4ffe-9170-5a81c4cca2eb',
        name: 'Jayant Bhawal',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/jayantbh.png'
      }
    },
    url: '/#',
    summary: ''
  },
  {
    id: 'ef8f4a4a-79c6-4502-851e-3070587db198',
    title: '',
    key: 'RggDoUbBNqr8hJ69dkHHoS',
    incident_number: 39738,
    provider: 'zenduty',
    status: 'resolved',
    service_id: '612468bc-c1ab-4ac8-b043-abfd5af05ed4',
    creation_date: '2023-08-06T00:02:23.174103+00:00',
    resolved_date: '2023-08-06T15:31:21.821769+00:00',
    acknowledged_date: '2023-08-06T00:02:45.291725+00:00',
    assigned_to: {
      username: 'jayantbh',
      linked_user: {
        id: '49dd63b5-2c89-43ad-9c35-26a2e516fd54',
        name: 'Jayant Bhawal',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/jayantbh.png'
      }
    },
    url: '/#',
    summary: ''
  },
  {
    id: 'dcc1abdd-0dcd-464c-b7c6-b8c4050edef6',
    title: '',
    key: 'RrZWykG7gaPaoGiBKogYR3',
    incident_number: 39755,
    provider: 'zenduty',
    status: 'resolved',
    service_id: '612468bc-c1ab-4ac8-b043-abfd5af05ed4',
    creation_date: '2023-08-07T05:49:24.550678+00:00',
    resolved_date: '2023-08-07T10:45:24.500896+00:00',
    acknowledged_date: '2023-08-07T05:49:41.458400+00:00',
    assigned_to: {
      username: 'jayantbh',
      linked_user: {
        id: '49dd63b5-2c89-43ad-9c35-26a2e516fd54',
        name: 'Jayant Bhawal',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/jayantbh.png'
      }
    },
    url: '/#',
    summary: ''
  },
  {
    id: '45dfc1c7-0ca9-4bdb-bb2d-6f0dde4e0584',
    title: '',
    key: 'mUwxsRAB2J3tPkgEYDqHpc',
    incident_number: 39756,
    provider: 'zenduty',
    status: 'resolved',
    service_id: '612468bc-c1ab-4ac8-b043-abfd5af05ed4',
    creation_date: '2023-08-07T05:49:24.557290+00:00',
    resolved_date: '2023-08-07T10:45:23.704187+00:00',
    acknowledged_date: '2023-08-07T05:49:45.068022+00:00',
    assigned_to: {
      username: 'jayantbh',
      linked_user: {
        id: '49dd63b5-2c89-43ad-9c35-26a2e516fd54',
        name: 'Jayant Bhawal',
        email: '<EMAIL>',
        avatar_url: 'https://github.com/jayantbh.png'
      }
    },
    url: '/#',
    summary: ''
  }
].map((incident) => ({
  ...incident,
  title: faker.lorem.sentence(),
  summary: faker.lorem.paragraphs(2)
}));
