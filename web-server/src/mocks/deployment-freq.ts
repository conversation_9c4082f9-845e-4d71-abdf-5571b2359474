export const mockDeploymentFreq = {
  deployments_map: {
    'e503a6b7-f14c-4a79-aa98-1797cef6928b': [] as const,
    'fa2d219b-c644-40e7-86ef-c976b40f2d23': [
      {
        id: '95b898ca-7770-44fa-beee-783936197c0c',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: '<PERSON><PERSON><PERSON>',
            email: '<EMAIL>',
            avatar_url: null as null
          }
        },
        created_at: '2023-03-30T15:37:20+00:00',
        updated_at: '2023-03-30T15:40:21+00:00',
        conducted_at: '2023-03-30T15:37:20+00:00',
        pr_count: 4,
        run_duration: 181,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4566280920',
        repo_workflow_id: '1c77d199-da92-44f0-8317-223b1ce981ca'
      },
      {
        id: 'd640333a-2e9a-4456-9dad-89a6f33fe1a8',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-30T15:16:40+00:00',
        updated_at: '2023-03-30T15:19:43+00:00',
        conducted_at: '2023-03-30T15:16:40+00:00',
        pr_count: 4,
        run_duration: 183,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4566075516',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '12709ed5-5754-4b3c-ad4f-2890d6639419',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-29T21:00:33+00:00',
        updated_at: '2023-03-29T21:03:43+00:00',
        conducted_at: '2023-03-29T21:00:33+00:00',
        pr_count: 6,
        run_duration: 190,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4557920095',
        repo_workflow_id: '1c77d199-da92-44f0-8317-223b1ce981ca'
      },
      {
        id: '43ec0cad-7887-4429-b96f-2d46cd3d448b',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-29T20:38:17+00:00',
        updated_at: '2023-03-29T20:41:11+00:00',
        conducted_at: '2023-03-29T20:38:17+00:00',
        pr_count: 1,
        run_duration: 174,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4557757820',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: 'd6ac1186-8b61-48e1-8f30-58132b47ad6f',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-29T14:09:10+00:00',
        updated_at: '2023-03-29T14:19:26+00:00',
        conducted_at: '2023-03-29T14:09:10+00:00',
        pr_count: 5,
        run_duration: 616,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4554970659',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '26060359-07e0-4980-b9e9-6f002ce852fa',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T16:47:22+00:00',
        updated_at: '2023-03-28T16:50:07+00:00',
        conducted_at: '2023-03-28T16:47:22+00:00',
        pr_count: 0,
        run_duration: 165,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4545550228',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '1bdba2d2-0583-4703-a270-881e68ca3300',
        status: 'FAILURE',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T16:21:57+00:00',
        updated_at: '2023-03-28T16:29:35+00:00',
        conducted_at: '2023-03-28T16:21:57+00:00',
        pr_count: 0,
        run_duration: 458,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4545355756',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '43e4046a-3f8e-4ebf-96cc-14c691b70597',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T16:08:59+00:00',
        updated_at: '2023-03-28T16:17:40+00:00',
        conducted_at: '2023-03-28T16:08:59+00:00',
        pr_count: 0,
        run_duration: 521,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4545247838',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '2882f2d9-fb56-49d5-8e56-d0935d431ebb',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T14:38:31+00:00',
        updated_at: '2023-03-28T14:41:20+00:00',
        conducted_at: '2023-03-28T14:38:31+00:00',
        pr_count: 1,
        run_duration: 169,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4544371546',
        repo_workflow_id: '1c77d199-da92-44f0-8317-223b1ce981ca'
      },
      {
        id: '14c714f3-0574-48ac-9228-2a8bb31eeb29',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T14:32:23+00:00',
        updated_at: '2023-03-28T14:35:29+00:00',
        conducted_at: '2023-03-28T14:32:23+00:00',
        pr_count: 9,
        run_duration: 186,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4544303801',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '179452ef-3525-4512-84bc-9351df588c77',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'amoghjalan',
          linked_user: {
            id: '97fb2060-9af2-4f4a-853b-ee1105f00b65',
            name: 'Amogh Jalan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T14:07:46+00:00',
        updated_at: '2023-03-28T14:10:34+00:00',
        conducted_at: '2023-03-28T14:07:46+00:00',
        pr_count: 8,
        run_duration: 168,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4544039532',
        repo_workflow_id: '1c77d199-da92-44f0-8317-223b1ce981ca'
      },
      {
        id: 'e85d49f3-b942-45df-a798-be232b61d8b7',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T07:47:55+00:00',
        updated_at: '2023-03-28T07:51:00+00:00',
        conducted_at: '2023-03-28T07:47:55+00:00',
        pr_count: 28,
        run_duration: 185,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4540574139',
        repo_workflow_id: '1c77d199-da92-44f0-8317-223b1ce981ca'
      },
      {
        id: 'df24d0cd-3009-4b48-a075-7f2a177cf710',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T07:24:01+00:00',
        updated_at: '2023-03-28T07:26:56+00:00',
        conducted_at: '2023-03-28T07:24:01+00:00',
        pr_count: 4,
        run_duration: 175,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4540388504',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: 'cd026e04-b4dd-4259-8f40-5cf1ac484ff6',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-27T09:55:45+00:00',
        updated_at: '2023-03-27T09:58:51+00:00',
        conducted_at: '2023-03-27T09:55:45+00:00',
        pr_count: 4,
        run_duration: 186,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4531092310',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '5dc9adcd-10dd-499f-a889-c2e8cbd1e5ff',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-21T16:49:51+00:00',
        updated_at: '2023-03-21T16:53:00+00:00',
        conducted_at: '2023-03-21T16:49:51+00:00',
        pr_count: 21,
        run_duration: 189,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4481673461',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: 'eab136fd-a5a3-4501-98a8-867d3cc15907',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-13T15:21:07+00:00',
        updated_at: '2023-03-13T15:24:14+00:00',
        conducted_at: '2023-03-13T15:21:07+00:00',
        pr_count: 24,
        run_duration: 187,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4406595632',
        repo_workflow_id: '1c77d199-da92-44f0-8317-223b1ce981ca'
      },
      {
        id: '9cacc09f-ea62-4ed5-8ccb-5b3ba925a0ff',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-13T12:13:58+00:00',
        updated_at: '2023-03-13T12:17:21+00:00',
        conducted_at: '2023-03-13T12:13:58+00:00',
        pr_count: 23,
        run_duration: 203,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4404756013',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '7202787a-e221-4fdf-805a-bd946b45e3a1',
        status: 'SUCCESS',
        head_branch: 'Widget',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-12T19:14:16+00:00',
        updated_at: '2023-03-12T19:17:24+00:00',
        conducted_at: '2023-03-12T19:14:16+00:00',
        pr_count: 0,
        run_duration: 188,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4399028972',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '569551db-8b4d-4942-8dd2-6523a9d89fcd',
        status: 'SUCCESS',
        head_branch: 'Widget',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-12T19:00:52+00:00',
        updated_at: '2023-03-12T19:04:29+00:00',
        conducted_at: '2023-03-12T19:00:52+00:00',
        pr_count: -1,
        run_duration: 217,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4398967579',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '63f71550-0db0-4384-a32c-4839d58ec1c7',
        status: 'SUCCESS',
        head_branch: 'addCreationDate',
        event_actor: {
          username: 'amoghjalan',
          linked_user: {
            id: '97fb2060-9af2-4f4a-853b-ee1105f00b65',
            name: 'Amogh Jalan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-10T13:32:47+00:00',
        updated_at: '2023-03-10T13:49:37+00:00',
        conducted_at: '2023-03-10T13:46:27+00:00',
        pr_count: 0,
        run_duration: 190,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4385002514',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '71a438fc-8400-4588-b151-ab1ea3f06ed5',
        status: 'FAILURE',
        head_branch: 'addCreationDate',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-10T13:43:02+00:00',
        updated_at: '2023-03-10T13:43:37+00:00',
        conducted_at: '2023-03-10T13:43:02+00:00',
        pr_count: 0,
        run_duration: 35,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4385089691',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: 'ae9578ce-e174-4e92-aed1-5145ef213e39',
        status: 'FAILURE',
        head_branch: 'addCreationDate',
        event_actor: {
          username: 'amoghjalan',
          linked_user: {
            id: '97fb2060-9af2-4f4a-853b-ee1105f00b65',
            name: 'Amogh Jalan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-10T13:30:54+00:00',
        updated_at: '2023-03-10T13:32:16+00:00',
        conducted_at: '2023-03-10T13:31:44+00:00',
        pr_count: 0,
        run_duration: 32,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4384988286',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: 'b3ca8c39-bb54-4cc9-8968-fa7d09864966',
        status: 'FAILURE',
        head_branch: 'addCreationDate',
        event_actor: {
          username: 'amoghjalan',
          linked_user: {
            id: '97fb2060-9af2-4f4a-853b-ee1105f00b65',
            name: 'Amogh Jalan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-10T12:56:32+00:00',
        updated_at: '2023-03-10T12:57:03+00:00',
        conducted_at: '2023-03-10T12:56:32+00:00',
        pr_count: 0,
        run_duration: 31,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4384700132',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '7c72114a-3b10-4b6f-95d3-e9bc67907483',
        status: 'FAILURE',
        head_branch: 'addCreationDate',
        event_actor: {
          username: 'amoghjalan',
          linked_user: {
            id: '97fb2060-9af2-4f4a-853b-ee1105f00b65',
            name: 'Amogh Jalan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-10T12:07:04+00:00',
        updated_at: '2023-03-10T12:07:34+00:00',
        conducted_at: '2023-03-10T12:07:04+00:00',
        pr_count: -1,
        run_duration: 30,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4384321463',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: 'a016f117-3680-43cc-b485-d61461c1fdd2',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-01T16:23:25+00:00',
        updated_at: '2023-03-01T16:26:39+00:00',
        conducted_at: '2023-03-01T16:23:25+00:00',
        pr_count: -1,
        run_duration: 194,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4305665071',
        repo_workflow_id: '1c77d199-da92-44f0-8317-223b1ce981ca'
      },
      {
        id: 'f69b2a99-ff66-4257-a7fe-a5dcae2b4665',
        status: 'SUCCESS',
        head_branch: 'master',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-01T16:16:51+00:00',
        updated_at: '2023-03-01T16:19:49+00:00',
        conducted_at: '2023-03-01T16:16:51+00:00',
        pr_count: -1,
        run_duration: 178,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4305606594',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: 'a17abd73-6310-472d-9c19-794fbcd62bbd',
        status: 'SUCCESS',
        head_branch: 'dbmigSettings',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-01T13:27:34+00:00',
        updated_at: '2023-03-01T13:30:34+00:00',
        conducted_at: '2023-03-01T13:27:34+00:00',
        pr_count: 0,
        run_duration: 180,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4304029037',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '8d77b686-877b-42ad-b30b-8bd5ca17fdc8',
        status: 'SUCCESS',
        head_branch: 'dbmigSettings',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-01T12:52:41+00:00',
        updated_at: '2023-03-01T12:55:37+00:00',
        conducted_at: '2023-03-01T12:52:41+00:00',
        pr_count: 0,
        run_duration: 176,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4303711870',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      },
      {
        id: '91166540-08e6-4766-aff5-daa00442ec47',
        status: 'SUCCESS',
        head_branch: 'dbmigSettings',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-01T12:34:00+00:00',
        updated_at: '2023-03-01T12:36:59+00:00',
        conducted_at: '2023-03-01T12:34:00+00:00',
        pr_count: -1,
        run_duration: 179,
        html_url:
          'https://github.com/monoclehq/monorepo/actions/runs/4303557644',
        repo_workflow_id: '2003f8d6-20b8-4dec-bc83-32837898351f'
      }
    ],
    '7fbb245d-e6ed-41a6-8fb8-177f67bb544a': [
      {
        id: 'fdccc762-5564-4b0e-8b1e-593d12e16352',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null as null
          }
        },
        created_at: '2023-04-06T00:03:12+00:00',
        updated_at: '2023-04-06T00:08:59+00:00',
        conducted_at: '2023-04-06T00:03:12+00:00',
        pr_count: 0,
        run_duration: 347,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4623976053',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'b7df77d5-642c-49fb-83b0-6d7960e39f2a',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-04-05T10:33:56+00:00',
        updated_at: '2023-04-05T10:40:41+00:00',
        conducted_at: '2023-04-05T10:33:56+00:00',
        pr_count: 0,
        run_duration: 405,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4617554757',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '6ca0d274-0cbc-4ebe-9559-015aa68e142b',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-04-05T04:18:00+00:00',
        updated_at: '2023-04-05T04:24:05+00:00',
        conducted_at: '2023-04-05T04:18:00+00:00',
        pr_count: 0,
        run_duration: 365,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4614879577',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '9eb7069f-29e7-407c-a69d-0bd3eb29a9ba',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-04-04T09:30:25+00:00',
        updated_at: '2023-04-04T09:36:28+00:00',
        conducted_at: '2023-04-04T09:30:25+00:00',
        pr_count: 0,
        run_duration: 363,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4606194746',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '0ae50d19-ac57-4058-a08e-decc2f27e3b5',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-04-04T07:10:06+00:00',
        updated_at: '2023-04-04T07:15:31+00:00',
        conducted_at: '2023-04-04T07:10:06+00:00',
        pr_count: 0,
        run_duration: 325,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4604992170',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'fb6f041f-dd49-4adc-8796-aed503cb8d60',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-04-03T23:51:03+00:00',
        updated_at: '2023-04-03T23:56:36+00:00',
        conducted_at: '2023-04-03T23:51:03+00:00',
        pr_count: 0,
        run_duration: 333,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4602402502',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'f1bc0615-393c-4234-b89c-e183565da355',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-04-03T19:53:56+00:00',
        updated_at: '2023-04-03T19:59:46+00:00',
        conducted_at: '2023-04-03T19:53:56+00:00',
        pr_count: 1,
        run_duration: 350,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4600757568',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: 'f5381a89-b987-4781-8a72-a432ba0a994a',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-04-03T19:53:41+00:00',
        updated_at: '2023-04-03T19:59:31+00:00',
        conducted_at: '2023-04-03T19:53:41+00:00',
        pr_count: 0,
        run_duration: 350,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4600755629',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '5a6defc2-ae89-4e0c-8875-36bb5547188d',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-04-03T19:53:20+00:00',
        updated_at: '2023-04-03T19:53:35+00:00',
        conducted_at: '2023-04-03T19:53:20+00:00',
        pr_count: 1,
        run_duration: 15,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4600752880',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '8e291d89-b3e8-4e03-8026-b94a3a180882',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-04-03T14:37:44+00:00',
        updated_at: '2023-04-03T14:46:19+00:00',
        conducted_at: '2023-04-03T14:37:44+00:00',
        pr_count: 3,
        run_duration: 515,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4597991981',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '3accd760-4162-4914-95cb-8d4e20e4efa1',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-04-03T11:08:37+00:00',
        updated_at: '2023-04-03T11:08:53+00:00',
        conducted_at: '2023-04-03T11:08:37+00:00',
        pr_count: 1,
        run_duration: 16,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4595957819',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '87467ecf-b5b7-4f70-a048-a48fb6b80556',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-31T12:46:42+00:00',
        updated_at: '2023-03-31T12:52:26+00:00',
        conducted_at: '2023-03-31T12:46:42+00:00',
        pr_count: 0,
        run_duration: 344,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4575185801',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'b785a94a-975d-4a13-839a-b4766296c8ef',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-31T12:46:11+00:00',
        updated_at: '2023-03-31T12:46:26+00:00',
        conducted_at: '2023-03-31T12:46:11+00:00',
        pr_count: 1,
        run_duration: 15,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4575180961',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '8d119270-fb1d-43c2-b363-0681063b7253',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-31T08:27:30+00:00',
        updated_at: '2023-03-31T08:33:42+00:00',
        conducted_at: '2023-03-31T08:27:30+00:00',
        pr_count: 1,
        run_duration: 372,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4573094128',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '2554cdee-8b23-4f52-993b-c4e9cab88373',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-31T06:58:51+00:00',
        updated_at: '2023-03-31T07:04:54+00:00',
        conducted_at: '2023-03-31T06:58:51+00:00',
        pr_count: 0,
        run_duration: 363,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4572425150',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '67ae9fbc-eab1-4d84-bf29-462c1d90ef03',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-31T06:58:39+00:00',
        updated_at: '2023-03-31T07:05:32+00:00',
        conducted_at: '2023-03-31T06:58:39+00:00',
        pr_count: 2,
        run_duration: 413,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4572423925',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '0870c386-90c0-48e3-a527-96674fb47dbe',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-31T00:02:23+00:00',
        updated_at: '2023-03-31T00:02:35+00:00',
        conducted_at: '2023-03-31T00:02:23+00:00',
        pr_count: 1,
        run_duration: 12,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4570122606',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '68abd2cb-b0ee-45be-9f6d-7609c33baf64',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-30T23:06:06+00:00',
        updated_at: '2023-03-30T23:13:37+00:00',
        conducted_at: '2023-03-30T23:06:06+00:00',
        pr_count: 1,
        run_duration: 451,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4569812910',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '6f06d984-632d-45ec-9194-e2553786196c',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-30T14:36:18+00:00',
        updated_at: '2023-03-30T14:43:17+00:00',
        conducted_at: '2023-03-30T14:36:18+00:00',
        pr_count: 1,
        run_duration: 419,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4565653462',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '57e088b6-c3c8-43dd-a4d6-d10e422a9324',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-30T14:36:12+00:00',
        updated_at: '2023-03-30T14:44:00+00:00',
        conducted_at: '2023-03-30T14:36:12+00:00',
        pr_count: 0,
        run_duration: 468,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4565652396',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'f1b965dc-eefb-4f6d-a2df-c6dc8b77b4ad',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-30T14:34:50+00:00',
        updated_at: '2023-03-30T14:35:06+00:00',
        conducted_at: '2023-03-30T14:34:50+00:00',
        pr_count: 1,
        run_duration: 16,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4565638220',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '913d4b87-a91e-48ad-848c-ea254d38b591',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-30T13:04:20+00:00',
        updated_at: '2023-03-30T13:10:02+00:00',
        conducted_at: '2023-03-30T13:04:20+00:00',
        pr_count: 0,
        run_duration: 342,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4564706783',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '7c88168f-b1a5-45d1-a50f-71193115ea33',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-30T12:25:21+00:00',
        updated_at: '2023-03-30T12:32:48+00:00',
        conducted_at: '2023-03-30T12:25:21+00:00',
        pr_count: 7,
        run_duration: 447,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4564328192',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '97bd1975-47f3-4465-bb81-bf3b801e3424',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-30T12:20:23+00:00',
        updated_at: '2023-03-30T12:20:37+00:00',
        conducted_at: '2023-03-30T12:20:23+00:00',
        pr_count: 1,
        run_duration: 14,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4564280727',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'eb1f019b-53c6-4d4f-89d8-6641c4f47f1f',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T22:59:32+00:00',
        updated_at: '2023-03-28T23:00:08+00:00',
        conducted_at: '2023-03-28T22:59:32+00:00',
        pr_count: 0,
        run_duration: 36,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4548410103',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '92ac03c3-a812-43de-ad70-cabdd89f0a5d',
        status: 'SUCCESS',
        head_branch: 'GROW-314',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T14:44:58+00:00',
        updated_at: '2023-03-28T14:51:52+00:00',
        conducted_at: '2023-03-28T14:44:58+00:00',
        pr_count: 0,
        run_duration: 414,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4544433354',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '9d8ba7d8-9fc3-4d2e-8668-1b2ccdfd48c7',
        status: 'SUCCESS',
        head_branch: 'GROW-314',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T11:44:39+00:00',
        updated_at: '2023-03-28T11:50:39+00:00',
        conducted_at: '2023-03-28T11:44:39+00:00',
        pr_count: 0,
        run_duration: 360,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4542653713',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: 'ca2bab7f-e954-4421-b1a9-d989180f4b51',
        status: 'SUCCESS',
        head_branch: 'GROW-314',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T10:15:11+00:00',
        updated_at: '2023-03-28T10:21:02+00:00',
        conducted_at: '2023-03-28T10:15:11+00:00',
        pr_count: -1,
        run_duration: 351,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4541905313',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '89c0d63e-290c-4328-8e4f-e6deabfc3ac1',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-28T00:01:15+00:00',
        updated_at: '2023-03-28T00:07:01+00:00',
        conducted_at: '2023-03-28T00:01:15+00:00',
        pr_count: 0,
        run_duration: 346,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4537744550',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '9dfebf45-68be-4e84-a051-f6e76de4254f',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-27T23:46:39+00:00',
        updated_at: '2023-03-27T23:53:07+00:00',
        conducted_at: '2023-03-27T23:46:39+00:00',
        pr_count: 0,
        run_duration: 388,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4537671599',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '61736ef3-8e97-4e4f-8af1-fc319730fc0b',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-27T23:44:28+00:00',
        updated_at: '2023-03-27T23:45:17+00:00',
        conducted_at: '2023-03-27T23:44:28+00:00',
        pr_count: 0,
        run_duration: 49,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4537660209',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '6f0e653e-71f8-495a-bc50-affe6020586a',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-24T14:46:09+00:00',
        updated_at: '2023-03-24T14:51:47+00:00',
        conducted_at: '2023-03-24T14:46:09+00:00',
        pr_count: 0,
        run_duration: 338,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4512404636',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '04741d3a-b785-43d8-a70c-1a284b6928fd',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-24T13:14:33+00:00',
        updated_at: '2023-03-24T13:21:56+00:00',
        conducted_at: '2023-03-24T13:14:33+00:00',
        pr_count: 0,
        run_duration: 443,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4511578676',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '9d8646a7-cdc5-455b-84d7-9daaba0d0851',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-23T19:58:33+00:00',
        updated_at: '2023-03-23T20:05:43+00:00',
        conducted_at: '2023-03-23T19:58:33+00:00',
        pr_count: 0,
        run_duration: 430,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4504689312',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '72e0a161-c865-4fd8-b47a-530d364c5687',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-22T19:40:28+00:00',
        updated_at: '2023-03-22T19:46:04+00:00',
        conducted_at: '2023-03-22T19:40:28+00:00',
        pr_count: 0,
        run_duration: 336,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4493825244',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '3f8876ff-0023-43bc-8a2d-780f6b5fd9bd',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-21T10:32:54+00:00',
        updated_at: '2023-03-21T10:33:10+00:00',
        conducted_at: '2023-03-21T10:32:54+00:00',
        pr_count: 1,
        run_duration: 16,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4478014493',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '4111b729-51b6-477f-afc9-292a435d43da',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-20T17:10:25+00:00',
        updated_at: '2023-03-20T17:10:42+00:00',
        conducted_at: '2023-03-20T17:10:25+00:00',
        pr_count: 1,
        run_duration: 17,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4470948189',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '1a2c839a-c60f-4738-b3dd-98956f733a38',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-17T13:07:31+00:00',
        updated_at: '2023-03-17T13:14:27+00:00',
        conducted_at: '2023-03-17T13:07:31+00:00',
        pr_count: 0,
        run_duration: 416,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4447810672',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'adb46c8c-e08a-46e4-b06a-989f1b200244',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-17T12:59:12+00:00',
        updated_at: '2023-03-17T13:04:59+00:00',
        conducted_at: '2023-03-17T12:59:12+00:00',
        pr_count: 0,
        run_duration: 347,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4447733107',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '354c6460-8302-44e8-8008-77e42d2bba2d',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-17T12:34:48+00:00',
        updated_at: '2023-03-17T12:40:34+00:00',
        conducted_at: '2023-03-17T12:34:48+00:00',
        pr_count: 0,
        run_duration: 346,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4447534507',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '372bcca0-656f-49ab-b905-786529564621',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-17T11:49:02+00:00',
        updated_at: '2023-03-17T11:57:13+00:00',
        conducted_at: '2023-03-17T11:49:02+00:00',
        pr_count: 0,
        run_duration: 491,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4447177677',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'f425639f-47d3-463b-b7ed-aec99df6080f',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-17T10:15:16+00:00',
        updated_at: '2023-03-17T10:21:05+00:00',
        conducted_at: '2023-03-17T10:15:16+00:00',
        pr_count: 1,
        run_duration: 349,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4446448461',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'eabf4203-b9bc-45d0-869a-3d12ef668fde',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-16T22:51:35+00:00',
        updated_at: '2023-03-16T22:57:16+00:00',
        conducted_at: '2023-03-16T22:51:35+00:00',
        pr_count: 0,
        run_duration: 341,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4442343620',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '26c83826-ccac-455e-984e-2b30c4dd4cb8',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-16T22:43:40+00:00',
        updated_at: '2023-03-16T22:49:21+00:00',
        conducted_at: '2023-03-16T22:43:40+00:00',
        pr_count: 0,
        run_duration: 341,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4442301063',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'eb006453-601c-4f9f-861b-7cc072f2dca2',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-16T22:09:46+00:00',
        updated_at: '2023-03-16T22:15:26+00:00',
        conducted_at: '2023-03-16T22:09:46+00:00',
        pr_count: 0,
        run_duration: 340,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4442079135',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '2d663ac3-19b4-47e2-a19d-652be85c519b',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-16T21:51:09+00:00',
        updated_at: '2023-03-16T21:58:25+00:00',
        conducted_at: '2023-03-16T21:51:09+00:00',
        pr_count: 0,
        run_duration: 436,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4441954504',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'edb19443-176d-4614-bef8-ef0781c6786f',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-16T21:43:47+00:00',
        updated_at: '2023-03-16T21:44:04+00:00',
        conducted_at: '2023-03-16T21:43:47+00:00',
        pr_count: 1,
        run_duration: 17,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4441909058',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '2251904e-1143-4d29-bd09-c4759ce29f74',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-16T11:05:25+00:00',
        updated_at: '2023-03-16T11:11:18+00:00',
        conducted_at: '2023-03-16T11:05:25+00:00',
        pr_count: 0,
        run_duration: 353,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4436331183',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'adf94f96-e7ec-4fb9-8ec6-2529c9c36e9c',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-16T10:48:04+00:00',
        updated_at: '2023-03-16T10:54:58+00:00',
        conducted_at: '2023-03-16T10:48:04+00:00',
        pr_count: 0,
        run_duration: 414,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4436186846',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '9f5b43c5-f2ab-4ab9-87d6-f0f4bba35816',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-16T10:34:33+00:00',
        updated_at: '2023-03-16T10:34:53+00:00',
        conducted_at: '2023-03-16T10:34:33+00:00',
        pr_count: 1,
        run_duration: 20,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4436074223',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'd2c43e6c-053f-4b8b-aedd-c5eb80847bd5',
        status: 'SUCCESS',
        head_branch: 'GROW-286',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-16T09:31:58+00:00',
        updated_at: '2023-03-16T09:38:28+00:00',
        conducted_at: '2023-03-16T09:31:58+00:00',
        pr_count: 0,
        run_duration: 390,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4435526878',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'ba0b3186-9285-46b1-980a-54224d46a918',
        status: 'SUCCESS',
        head_branch: 'GROW-286',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-16T09:31:50+00:00',
        updated_at: '2023-03-16T09:38:14+00:00',
        conducted_at: '2023-03-16T09:31:50+00:00',
        pr_count: 0,
        run_duration: 384,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4435525713',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: 'ff950d40-fc1d-4e2d-bd31-c6ba26c3d1b5',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-16T09:01:56+00:00',
        updated_at: '2023-03-16T09:03:20+00:00',
        conducted_at: '2023-03-16T09:01:56+00:00',
        pr_count: 0,
        run_duration: 84,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4435265086',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'fceec0e9-3dcd-4303-8c82-6a6f12197ea4',
        status: 'SUCCESS',
        head_branch: 'GROW-286',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-16T08:24:48+00:00',
        updated_at: '2023-03-16T08:30:50+00:00',
        conducted_at: '2023-03-16T08:24:48+00:00',
        pr_count: -1,
        run_duration: 362,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4434976283',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '77835597-9861-4287-b6f8-143952b23835',
        status: 'SUCCESS',
        head_branch: 'GROW-286',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-15T22:58:36+00:00',
        updated_at: '2023-03-15T23:04:18+00:00',
        conducted_at: '2023-03-15T22:58:36+00:00',
        pr_count: 0,
        run_duration: 342,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4431705441',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '85b23f96-2027-4dbf-9980-e98c3233e59a',
        status: 'SUCCESS',
        head_branch: 'GROW-286',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-15T19:33:00+00:00',
        updated_at: '2023-03-15T19:38:51+00:00',
        conducted_at: '2023-03-15T19:33:00+00:00',
        pr_count: 0,
        run_duration: 351,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4430221451',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: 'c43e33f0-5be2-40e2-b454-8213ed0e70c3',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-15T18:44:29+00:00',
        updated_at: '2023-03-15T18:44:42+00:00',
        conducted_at: '2023-03-15T18:44:29+00:00',
        pr_count: 1,
        run_duration: 13,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4429837773',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'bde3c466-9920-4ca9-a760-473acab062d4',
        status: 'SUCCESS',
        head_branch: 'Widget',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-15T16:49:22+00:00',
        updated_at: '2023-03-15T16:55:23+00:00',
        conducted_at: '2023-03-15T16:49:22+00:00',
        pr_count: 0,
        run_duration: 361,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4428802828',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '129e8d69-7b09-4554-843e-1400b431b5e3',
        status: 'SUCCESS',
        head_branch: 'Widget',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-15T14:02:30+00:00',
        updated_at: '2023-03-15T14:08:01+00:00',
        conducted_at: '2023-03-15T14:02:30+00:00',
        pr_count: 0,
        run_duration: 331,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4427163622',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '016b8fbe-1d2f-432c-b7a1-3fceecd4bc9f',
        status: 'SUCCESS',
        head_branch: 'Widget',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-15T13:49:46+00:00',
        updated_at: '2023-03-15T13:57:38+00:00',
        conducted_at: '2023-03-15T13:49:46+00:00',
        pr_count: 0,
        run_duration: 472,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4427041639',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '21e46ff1-726b-455a-a029-4edacf0527b5',
        status: 'SUCCESS',
        head_branch: 'Widget',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-15T13:09:32+00:00',
        updated_at: '2023-03-15T13:15:21+00:00',
        conducted_at: '2023-03-15T13:09:32+00:00',
        pr_count: 0,
        run_duration: 349,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4426659144',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '81d42256-0fb8-4c78-b6bb-9b629d715731',
        status: 'SUCCESS',
        head_branch: 'staging',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-15T12:20:13+00:00',
        updated_at: '2023-03-15T12:28:28+00:00',
        conducted_at: '2023-03-15T12:20:13+00:00',
        pr_count: 0,
        run_duration: 495,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4426210908',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: 'f788178d-8c2a-4f4f-a692-2d31d0eb6738',
        status: 'SUCCESS',
        head_branch: 'Widget',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-15T07:48:56+00:00',
        updated_at: '2023-03-15T07:54:50+00:00',
        conducted_at: '2023-03-15T07:48:56+00:00',
        pr_count: 0,
        run_duration: 354,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4423945659',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '763840f3-026f-4d9e-944c-c6fc7fd571bc',
        status: 'SUCCESS',
        head_branch: 'GROW-286',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-15T07:10:06+00:00',
        updated_at: '2023-03-15T07:15:58+00:00',
        conducted_at: '2023-03-15T07:10:06+00:00',
        pr_count: 0,
        run_duration: 352,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4423701397',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '5b37b1a1-f9aa-4726-8dd3-24386f7a6bef',
        status: 'SUCCESS',
        head_branch: 'Widget',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-15T06:45:00+00:00',
        updated_at: '2023-03-15T06:51:09+00:00',
        conducted_at: '2023-03-15T06:45:00+00:00',
        pr_count: 0,
        run_duration: 369,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4423548494',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: 'd137c755-9cc8-48af-831e-e73e5532b91c',
        status: 'SUCCESS',
        head_branch: 'GROW-286',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-14T22:10:07+00:00',
        updated_at: '2023-03-14T22:15:57+00:00',
        conducted_at: '2023-03-14T22:10:07+00:00',
        pr_count: -1,
        run_duration: 350,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4420640896',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '6bfeab5a-f0c3-45ed-9f3c-b3c0973447ae',
        status: 'SUCCESS',
        head_branch: 'Widget',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-14T13:55:07+00:00',
        updated_at: '2023-03-14T14:00:58+00:00',
        conducted_at: '2023-03-14T13:55:07+00:00',
        pr_count: 0,
        run_duration: 351,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4416484746',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: 'ba8c43bb-3257-4787-bd13-ce52cd7fd84b',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-13T22:05:14+00:00',
        updated_at: '2023-03-13T22:11:20+00:00',
        conducted_at: '2023-03-13T22:05:14+00:00',
        pr_count: 0,
        run_duration: 366,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4409945504',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'c9ebea73-ca5b-4409-a9f4-f6bcaad22fcc',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-13T20:22:33+00:00',
        updated_at: '2023-03-13T20:28:17+00:00',
        conducted_at: '2023-03-13T20:22:33+00:00',
        pr_count: 0,
        run_duration: 344,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4409201639',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'de878794-e132-4677-9159-5e6d45e944ed',
        status: 'SUCCESS',
        head_branch: 'staging',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-12T22:16:23+00:00',
        updated_at: '2023-03-12T22:22:16+00:00',
        conducted_at: '2023-03-12T22:16:23+00:00',
        pr_count: 0,
        run_duration: 353,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4399700010',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '731d5c22-ece5-4a41-8342-90a6470a0f6e',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-12T20:49:51+00:00',
        updated_at: '2023-03-12T20:55:30+00:00',
        conducted_at: '2023-03-12T20:49:51+00:00',
        pr_count: 0,
        run_duration: 339,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4399378473',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'f450ad3c-236a-4e9b-9131-6c16ccc4d748',
        status: 'SUCCESS',
        head_branch: 'staging',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-12T19:07:59+00:00',
        updated_at: '2023-03-12T19:13:31+00:00',
        conducted_at: '2023-03-12T19:07:59+00:00',
        pr_count: 0,
        run_duration: 332,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4399001490',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: 'cec20048-2dba-4274-94db-0908dd382494',
        status: 'SUCCESS',
        head_branch: 'Widget',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-12T15:32:20+00:00',
        updated_at: '2023-03-12T15:37:54+00:00',
        conducted_at: '2023-03-12T15:32:20+00:00',
        pr_count: -1,
        run_duration: 334,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4398184280',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '529ca061-5c56-4f19-ae97-78ef989d028c',
        status: 'SUCCESS',
        head_branch: 'staging',
        event_actor: {
          username: 'samad-yar-khan',
          linked_user: {
            id: '391420ef-b113-4267-8c08-76c0f5140413',
            name: 'Samad Yar Khan',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-10T23:56:31+00:00',
        updated_at: '2023-03-11T00:02:28+00:00',
        conducted_at: '2023-03-10T23:56:31+00:00',
        pr_count: 0,
        run_duration: 357,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4389425037',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '38aa0eb1-7aae-4355-8f11-f5226e5429fc',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-10T06:46:11+00:00',
        updated_at: '2023-03-10T06:52:02+00:00',
        conducted_at: '2023-03-10T06:46:11+00:00',
        pr_count: 0,
        run_duration: 351,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4382048900',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'add2a241-364e-4250-a608-beef69dd1939',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-10T06:45:54+00:00',
        updated_at: '2023-03-10T06:51:24+00:00',
        conducted_at: '2023-03-10T06:45:54+00:00',
        pr_count: 1,
        run_duration: 330,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4382047260',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '95145a10-86f0-4397-a3e2-b85744ca4be8',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-10T06:45:18+00:00',
        updated_at: '2023-03-10T06:45:32+00:00',
        conducted_at: '2023-03-10T06:45:18+00:00',
        pr_count: 1,
        run_duration: 14,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4382043739',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'f1d503cc-fa7b-400d-8e2a-4424c2bc5915',
        status: 'SUCCESS',
        head_branch: 'GROW-252',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-09T21:47:48+00:00',
        updated_at: '2023-03-09T21:53:22+00:00',
        conducted_at: '2023-03-09T21:47:48+00:00',
        pr_count: 0,
        run_duration: 334,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4379067886',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '90358d22-25ec-415a-82df-bbecde25b5c8',
        status: 'SUCCESS',
        head_branch: 'GROW-252',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-09T19:11:21+00:00',
        updated_at: '2023-03-09T19:17:01+00:00',
        conducted_at: '2023-03-09T19:11:21+00:00',
        pr_count: 0,
        run_duration: 340,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4377939809',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '212d1b64-93a3-408d-8d84-c3affe9d0943',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-09T16:54:05+00:00',
        updated_at: '2023-03-09T17:00:04+00:00',
        conducted_at: '2023-03-09T16:54:05+00:00',
        pr_count: 0,
        run_duration: 359,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4376826381',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'a41dc0f8-0b24-42b8-8261-7a8fd119e10a',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-09T16:14:25+00:00',
        updated_at: '2023-03-09T16:20:02+00:00',
        conducted_at: '2023-03-09T16:14:25+00:00',
        pr_count: 0,
        run_duration: 337,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4376475810',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '0e7652ac-12d9-4008-a451-70acf757c6b2',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-09T13:44:46+00:00',
        updated_at: '2023-03-09T13:51:22+00:00',
        conducted_at: '2023-03-09T13:44:46+00:00',
        pr_count: 0,
        run_duration: 396,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4375073503',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'eeededff-ccae-4c46-9325-7a2bd0b63ada',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-09T07:15:22+00:00',
        updated_at: '2023-03-09T07:20:59+00:00',
        conducted_at: '2023-03-09T07:15:22+00:00',
        pr_count: 0,
        run_duration: 337,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4371998470',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '4d9f9f16-875b-49b3-8cac-053e01ab7c84',
        status: 'SUCCESS',
        head_branch: 'GROW-252',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-08T17:13:39+00:00',
        updated_at: '2023-03-08T17:19:52+00:00',
        conducted_at: '2023-03-08T17:13:39+00:00',
        pr_count: -1,
        run_duration: 373,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4366774441',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: 'a84307c1-ac1f-4048-8b51-513daa0cb14e',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-07T10:05:11+00:00',
        updated_at: '2023-03-07T10:12:40+00:00',
        conducted_at: '2023-03-07T10:05:11+00:00',
        pr_count: 0,
        run_duration: 449,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4352925185',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '5f6528d0-68ab-4328-b0c4-3762012a6b6d',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-06T20:46:30+00:00',
        updated_at: '2023-03-06T20:53:45+00:00',
        conducted_at: '2023-03-06T20:46:30+00:00',
        pr_count: 0,
        run_duration: 435,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4347804137',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '87c2d287-26c2-4b89-b84d-299838dcb21a',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-06T20:42:35+00:00',
        updated_at: '2023-03-06T20:48:16+00:00',
        conducted_at: '2023-03-06T20:42:35+00:00',
        pr_count: 3,
        run_duration: 341,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4347776865',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: 'bee0b354-0b5f-4627-b6ae-1dae99763221',
        status: 'FAILURE',
        head_branch: 'GROW-270',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-06T20:42:17+00:00',
        updated_at: '2023-03-06T20:42:34+00:00',
        conducted_at: '2023-03-06T20:42:17+00:00',
        pr_count: 0,
        run_duration: 17,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4347774826',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '5b2e4dfa-18b2-46c8-a175-35d2c5dd0cde',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-06T20:39:35+00:00',
        updated_at: '2023-03-06T20:39:47+00:00',
        conducted_at: '2023-03-06T20:39:35+00:00',
        pr_count: 1,
        run_duration: 12,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4347753370',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'bc4a9192-51d1-428d-81db-535a85d69612',
        status: 'SUCCESS',
        head_branch: 'GROW-270',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-06T18:46:43+00:00',
        updated_at: '2023-03-06T18:52:24+00:00',
        conducted_at: '2023-03-06T18:46:43+00:00',
        pr_count: 0,
        run_duration: 341,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4346852104',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '4ae8da4f-0503-4949-ad37-764a84b65db8',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-06T12:21:18+00:00',
        updated_at: '2023-03-06T12:26:45+00:00',
        conducted_at: '2023-03-06T12:21:18+00:00',
        pr_count: 0,
        run_duration: 327,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4343340940',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'e4dc4a75-08df-4cf5-a1b2-0ea81a45a51f',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-06T11:48:16+00:00',
        updated_at: '2023-03-06T11:53:49+00:00',
        conducted_at: '2023-03-06T11:48:16+00:00',
        pr_count: 0,
        run_duration: 333,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4343061491',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '417918bc-959d-43d1-96bb-c766bfc5d3df',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-06T11:23:00+00:00',
        updated_at: '2023-03-06T11:29:58+00:00',
        conducted_at: '2023-03-06T11:23:00+00:00',
        pr_count: 1,
        run_duration: 418,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4342869680',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '675b9c0f-8615-49ad-9e50-f504bdf7bb7e',
        status: 'SUCCESS',
        head_branch: 'GROW-270',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-04T19:04:26+00:00',
        updated_at: '2023-03-04T19:10:10+00:00',
        conducted_at: '2023-03-04T19:04:26+00:00',
        pr_count: 0,
        run_duration: 344,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4332425772',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: 'a71cdc00-05a2-4dbf-aa49-f341e193a586',
        status: 'SUCCESS',
        head_branch: 'GROW-270',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-04T17:42:58+00:00',
        updated_at: '2023-03-04T17:50:30+00:00',
        conducted_at: '2023-03-04T17:42:58+00:00',
        pr_count: 0,
        run_duration: 452,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4332135243',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '7e9cf7c5-d33d-4321-bfdc-a92dace4030d',
        status: 'SUCCESS',
        head_branch: 'GROW-270',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-04T17:13:38+00:00',
        updated_at: '2023-03-04T17:20:54+00:00',
        conducted_at: '2023-03-04T17:13:38+00:00',
        pr_count: 0,
        run_duration: 436,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4332025663',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '94ec4669-4edd-487c-a917-43c55fa11ed9',
        status: 'SUCCESS',
        head_branch: 'GROW-270',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-03T22:02:16+00:00',
        updated_at: '2023-03-03T22:08:05+00:00',
        conducted_at: '2023-03-03T22:02:16+00:00',
        pr_count: -1,
        run_duration: 349,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4327615541',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: 'fba5aaef-bc16-43d4-b061-dabd663a3c1b',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-03T21:21:51+00:00',
        updated_at: '2023-03-03T21:28:32+00:00',
        conducted_at: '2023-03-03T21:21:51+00:00',
        pr_count: 1,
        run_duration: 401,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4327388718',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '3631d42f-9a9f-4a75-a35f-8a827434280d',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-03T21:05:40+00:00',
        updated_at: '2023-03-03T21:11:27+00:00',
        conducted_at: '2023-03-03T21:05:40+00:00',
        pr_count: 0,
        run_duration: 347,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4327279041',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '36011035-cf3c-4754-b3c6-a0040e18260b',
        status: 'SUCCESS',
        head_branch: 'staging',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-03T20:56:38+00:00',
        updated_at: '2023-03-03T21:02:31+00:00',
        conducted_at: '2023-03-03T20:56:38+00:00',
        pr_count: -1,
        run_duration: 353,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4327217553',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '11500e61-36b2-46f0-9f05-b1895037328f',
        status: 'SUCCESS',
        head_branch: 'auth-fix',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-02T11:55:15+00:00',
        updated_at: '2023-03-02T12:01:05+00:00',
        conducted_at: '2023-03-02T11:55:15+00:00',
        pr_count: -1,
        run_duration: 350,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4313691531',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '550030f9-00b7-4cf8-8327-9ca360978799',
        status: 'SUCCESS',
        head_branch: 'auth-fix',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-02T11:54:16+00:00',
        updated_at: '2023-03-02T11:59:57+00:00',
        conducted_at: '2023-03-02T11:54:16+00:00',
        pr_count: -1,
        run_duration: 341,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4313684926',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'e0a53131-e078-4d53-ba26-4a36f0a71a1a',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-02T11:53:29+00:00',
        updated_at: '2023-03-02T11:54:11+00:00',
        conducted_at: '2023-03-02T11:53:29+00:00',
        pr_count: 0,
        run_duration: 42,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4313679383',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '13e24b23-7cec-484b-b99a-4c4afb95cc9b',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-02T10:58:59+00:00',
        updated_at: '2023-03-02T11:05:11+00:00',
        conducted_at: '2023-03-02T10:58:59+00:00',
        pr_count: 0,
        run_duration: 372,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4313247772',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '8a5c6d97-c692-4b4e-9263-a072b407745e',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-01T22:04:46+00:00',
        updated_at: '2023-03-01T22:10:25+00:00',
        conducted_at: '2023-03-01T22:04:46+00:00',
        pr_count: 0,
        run_duration: 339,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4308365876',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'd4d7745d-80f7-4aeb-8a06-4c13e54f44ce',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-01T21:53:17+00:00',
        updated_at: '2023-03-01T21:58:58+00:00',
        conducted_at: '2023-03-01T21:53:17+00:00',
        pr_count: -1,
        run_duration: 341,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4308288485',
        repo_workflow_id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0'
      },
      {
        id: '013d638e-601d-46eb-9ae4-1e9fd70a227e',
        status: 'FAILURE',
        head_branch: 'main',
        event_actor: {
          username: 'shivam-bit',
          linked_user: {
            id: 'd3731fae-68e9-4ef5-92fb-a7cfb228b888',
            name: 'Shivam Singh',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-01T21:51:58+00:00',
        updated_at: '2023-03-01T21:52:16+00:00',
        conducted_at: '2023-03-01T21:51:58+00:00',
        pr_count: 1,
        run_duration: 18,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4308279748',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '8ad43bc8-0c9b-4989-b250-597d20412212',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-01T11:32:29+00:00',
        updated_at: '2023-03-01T11:38:27+00:00',
        conducted_at: '2023-03-01T11:32:29+00:00',
        pr_count: 0,
        run_duration: 358,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4303068979',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: '81616db4-a9ba-4798-9654-24ea20103478',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-03-01T10:22:38+00:00',
        updated_at: '2023-03-01T10:28:33+00:00',
        conducted_at: '2023-03-01T10:22:38+00:00',
        pr_count: 0,
        run_duration: 355,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4302488807',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      },
      {
        id: 'b45eef79-5b37-4349-98a4-69a67252d3d5',
        status: 'SUCCESS',
        head_branch: 'main',
        event_actor: {
          username: 'jayantbh',
          linked_user: {
            id: '5b874fac-a4cf-4290-b07d-d79915102879',
            name: 'Jayant Bhawal',
            email: '<EMAIL>',
            avatar_url: null
          }
        },
        created_at: '2023-02-27T21:55:56+00:00',
        updated_at: '2023-02-27T22:01:27+00:00',
        conducted_at: '2023-02-27T21:55:56+00:00',
        pr_count: -1,
        run_duration: 331,
        html_url:
          'https://github.com/monoclehq/web-manager-dash/actions/runs/4287198663',
        repo_workflow_id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788'
      }
    ]
  },
  repos_map: {
    'e503a6b7-f14c-4a79-aa98-1797cef6928b': {
      id: 'e503a6b7-f14c-4a79-aa98-1797cef6928b',
      name: 'dum-e',
      language: null as null,
      default_branch: 'main',
      parent: 'monoclehq'
    },
    'fa2d219b-c644-40e7-86ef-c976b40f2d23': {
      id: 'fa2d219b-c644-40e7-86ef-c976b40f2d23',
      name: 'monorepo',
      language: 'Python',
      default_branch: 'master',
      parent: 'monoclehq'
    },
    '7fbb245d-e6ed-41a6-8fb8-177f67bb544a': {
      id: '7fbb245d-e6ed-41a6-8fb8-177f67bb544a',
      name: 'web-manager-dash',
      language: 'TypeScript',
      default_branch: 'main',
      parent: 'monoclehq'
    }
  },
  workflows_map: {
    '2003f8d6-20b8-4dec-bc83-32837898351f': {
      id: '2003f8d6-20b8-4dec-bc83-32837898351f',
      name: 'Stage Migration',
      repo_id: 'fa2d219b-c644-40e7-86ef-c976b40f2d23',
      created_at: '2023-03-28T22:18:16.288452+00:00',
      updated_at: '2023-03-28T22:18:16.288452+00:00',
      type: 'DEPLOYMENT',
      provider: 'github'
    },
    '1c77d199-da92-44f0-8317-223b1ce981ca': {
      id: '1c77d199-da92-44f0-8317-223b1ce981ca',
      name: 'Prod Migration',
      repo_id: 'fa2d219b-c644-40e7-86ef-c976b40f2d23',
      created_at: '2023-03-28T22:18:16.288452+00:00',
      updated_at: '2023-03-28T22:18:16.288452+00:00',
      type: 'DEPLOYMENT',
      provider: 'github'
    },
    '6415b349-1f49-4f97-96f9-dc5ac8eaf788': {
      id: '6415b349-1f49-4f97-96f9-dc5ac8eaf788',
      name: 'Production Deployment',
      repo_id: '7fbb245d-e6ed-41a6-8fb8-177f67bb544a',
      created_at: '2023-01-30T08:33:24.206130+00:00',
      updated_at: '2023-01-30T08:33:24.206130+00:00',
      type: 'DEPLOYMENT',
      provider: 'github'
    },
    '9d790dd5-bcf8-4afd-8f9b-9561709fffc0': {
      id: '9d790dd5-bcf8-4afd-8f9b-9561709fffc0',
      name: 'Staging Deployment',
      repo_id: '7fbb245d-e6ed-41a6-8fb8-177f67bb544a',
      created_at: '2023-02-02T16:38:30.920734+00:00',
      updated_at: '2023-02-02T16:38:30.920734+00:00',
      type: 'DEPLOYMENT',
      provider: 'github'
    }
  }
};
