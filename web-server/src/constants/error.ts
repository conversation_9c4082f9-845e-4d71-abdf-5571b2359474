export enum Errors {
  INTEGRATION_NOT_FOUND = 'INTEGRATION_NOT_FOUND',
  USER_IDENTITY_UID_NOT_FOUND = 'USER_IDENTITY_UID_NOT_FOUND',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  SESSION_USER_NOT_FOUND = 'SESSION_USER_NOT_FOUND',
  MISSING_USER_ID_IN_COOKIE = 'MISSING_USER_ID_IN_COOKIE',
  METHOD_NOT_ALLOWED = 'METHOD_NOT_ALLOWED', // Accompany with status 405
  INSUFFICIENT_PARAMS = 'INSUFFICIENT_PARAMS',
  VIEW_AS_PROCEDURE_FAILED = 'VIEW_AS_PROCEDURE_FAILED',
  ACCESS_DENIED = 'ACCESS_DENIED', // use with 403
  EXCEEDED_UPDATE_THRESHOLD = 'EXCEEDED_UPDATE_THRESHOLD',
  UNHANDLED_SLACK_ACTION = 'UNHANDLED_SLACK_ACTION',
  SLACK_INTEGRATION_MISSING = 'SLACK_INTEGRATION_MISSING',
  SLACK_IDENTIFIER_MISSING = 'SLACK_IDENTIFIER_MISSING',
  SLACK_TEAM_OR_USER_MISSING = 'SLACK_TEAM_OR_USER_MISSING',
  ORG_DENYLISTED = 'ORG_DENYLISTED',
  ORG_PENDING_APPROVAL = 'ORG_PENDING_APPROVAL',
  SAME_USER_ONEONONE_ATTEMPT = 'SAME_USER_ONEONONE_ATTEMPT',
  UNRELATED_USER_ATTEMPT = 'UNRELATED_USER_ATTEMPT',
  TRIAL_EXPIRED = 'TRIAL_EXPIRED',
  PLAN_EXPIRED = 'PLAN_EXPIRED',
  USER_ID_OR_ORG_ID_MISSING = 'USER_ID_OR_ORG_ID_PARAMS_MISSING',
  NO_NODES_FOUND_FOR_AI_ANALYSIS = 'NO_NODES_FOUND_FOR_AI_ANALYSIS',
  UNHANDLED_API_ERROR = 'UNHANDLED_API_ERROR'
}

const defaultResponseCodes = {
  [Errors.SESSION_USER_NOT_FOUND]: 401,
  [Errors.ACCESS_DENIED]: 403,
  [Errors.ORG_DENYLISTED]: 403,
  [Errors.ORG_PENDING_APPROVAL]: 403,
  [Errors.MISSING_USER_ID_IN_COOKIE]: 403,
  [Errors.TRIAL_EXPIRED]: 403,
  [Errors.PLAN_EXPIRED]: 403,
  [Errors.METHOD_NOT_ALLOWED]: 405,
  [Errors.INTEGRATION_NOT_FOUND]: 404,
  [Errors.USER_NOT_FOUND]: 404,
  [Errors.SLACK_INTEGRATION_MISSING]: 404,
  [Errors.SAME_USER_ONEONONE_ATTEMPT]: 401,
  [Errors.UNHANDLED_API_ERROR]: 500
} as const;

export class ResponseError extends Error {
  status: number;
  constructor(msg: Errors | string, status?: number) {
    super();
    this.name = 'ResponseError';
    this.status =
      status ||
      defaultResponseCodes[msg as keyof typeof defaultResponseCodes] ||
      400;
    this.message = msg;
  }
}

export enum InternalAPIErrors {
  GOOGLE_TOKEN_NOT_FOUND = 'GOOGLE_TOKEN_NOT_FOUND',
  GOOGLE_TOKEN_EXPIRED = 'GOOGLE_TOKEN_EXPIRED',
  GITHUB_TOKEN_NOT_FOUND = 'GITHUB_TOKEN_NOT_FOUND',
  GITHUB_TOKEN_EXPIRED = 'GITHUB_TOKEN_EXPIRED',
  JIRA_TOKEN_NOT_FOUND = 'JIRA_TOKEN_NOT_FOUND',
  JIRA_TOKEN_EXPIRED = 'JIRA_TOKEN_EXPIRED',
  'Bad credentials' = 'Bad credentials',
  ORG_TREE_NOT_FOUND = 'ORG_TREE_NOT_FOUND',
  SEAT_LIMIT_EXCEEDED = 'SEAT_LIMIT_EXCEEDED'
}

export enum SeatLimitExceededSource {
  SINGLE_USER = 'SINGLE_USER',
  BULK_USER = 'BULK_USER'
}

export const ForwardableErrors = new Set([]);
export const ForwardableWarnings = new Set([
  'Suggested Manager already reporting to the Reportee'
]);
