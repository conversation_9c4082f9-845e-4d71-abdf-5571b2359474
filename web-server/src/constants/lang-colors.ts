// 20220707022753
// https://raw.githubusercontent.com/ozh/github-colors/master/colors.json

// NOTE: LANGUAGE NAMES ARE LOWERCASED

export const langColors: Record<string, { color: string; url: string }> = {
  '1c enterprise': {
    color: '#814CCC',
    url: 'https://github.com/trending?l=1C-Enterprise'
  },
  '2-dimensional array': {
    color: '#38761D',
    url: 'https://github.com/trending?l=2-Dimensional-Array'
  },
  '4d': {
    color: '#004289',
    url: 'https://github.com/trending?l=4D'
  },
  abap: {
    color: '#E8274B',
    url: 'https://github.com/trending?l=ABAP'
  },
  'abap cds': {
    color: '#555e25',
    url: 'https://github.com/trending?l=ABAP-CDS'
  },
  actionscript: {
    color: '#882B0F',
    url: 'https://github.com/trending?l=ActionScript'
  },
  ada: {
    color: '#02f88c',
    url: 'https://github.com/trending?l=Ada'
  },
  'adobe font metrics': {
    color: '#fa0f00',
    url: 'https://github.com/trending?l=Adobe-Font-Metrics'
  },
  agda: {
    color: '#315665',
    url: 'https://github.com/trending?l=Agda'
  },
  'ags script': {
    color: '#B9D9FF',
    url: 'https://github.com/trending?l=AGS-Script'
  },
  aidl: {
    color: '#34EB6B',
    url: 'https://github.com/trending?l=AIDL'
  },
  al: {
    color: '#3AA2B5',
    url: 'https://github.com/trending?l=AL'
  },
  alloy: {
    color: '#64C800',
    url: 'https://github.com/trending?l=Alloy'
  },
  'alpine abuild': {
    color: '#0D597F',
    url: 'https://github.com/trending?l=Alpine-Abuild'
  },
  'altium designer': {
    color: '#A89663',
    url: 'https://github.com/trending?l=Altium-Designer'
  },
  ampl: {
    color: '#E6EFBB',
    url: 'https://github.com/trending?l=AMPL'
  },
  angelscript: {
    color: '#C7D7DC',
    url: 'https://github.com/trending?l=AngelScript'
  },
  'ant build system': {
    color: '#A9157E',
    url: 'https://github.com/trending?l=Ant-Build-System'
  },
  antlers: {
    color: '#ff269e',
    url: 'https://github.com/trending?l=Antlers'
  },
  antlr: {
    color: '#9DC3FF',
    url: 'https://github.com/trending?l=ANTLR'
  },
  apacheconf: {
    color: '#d12127',
    url: 'https://github.com/trending?l=ApacheConf'
  },
  apex: {
    color: '#1797c0',
    url: 'https://github.com/trending?l=Apex'
  },
  'api blueprint': {
    color: '#2ACCA8',
    url: 'https://github.com/trending?l=API-Blueprint'
  },
  apl: {
    color: '#5A8164',
    url: 'https://github.com/trending?l=APL'
  },
  'apollo guidance computer': {
    color: '#0B3D91',
    url: 'https://github.com/trending?l=Apollo-Guidance-Computer'
  },
  applescript: {
    color: '#101F1F',
    url: 'https://github.com/trending?l=AppleScript'
  },
  arc: {
    color: '#aa2afe',
    url: 'https://github.com/trending?l=Arc'
  },
  asciidoc: {
    color: '#73a0c5',
    url: 'https://github.com/trending?l=AsciiDoc'
  },
  'asp.net': {
    color: '#9400ff',
    url: 'https://github.com/trending?l=ASP.NET'
  },
  aspectj: {
    color: '#a957b0',
    url: 'https://github.com/trending?l=AspectJ'
  },
  assembly: {
    color: '#6E4C13',
    url: 'https://github.com/trending?l=Assembly'
  },
  astro: {
    color: '#ff5a03',
    url: 'https://github.com/trending?l=Astro'
  },
  asymptote: {
    color: '#ff0000',
    url: 'https://github.com/trending?l=Asymptote'
  },
  ats: {
    color: '#1ac620',
    url: 'https://github.com/trending?l=ATS'
  },
  augeas: {
    color: '#9CC134',
    url: 'https://github.com/trending?l=Augeas'
  },
  autohotkey: {
    color: '#6594b9',
    url: 'https://github.com/trending?l=AutoHotkey'
  },
  autoit: {
    color: '#1C3552',
    url: 'https://github.com/trending?l=AutoIt'
  },
  'avro idl': {
    color: '#0040FF',
    url: 'https://github.com/trending?l=Avro-IDL'
  },
  awk: {
    color: '#c30e9b',
    url: 'https://github.com/trending?l=Awk'
  },
  ballerina: {
    color: '#FF5000',
    url: 'https://github.com/trending?l=Ballerina'
  },
  basic: {
    color: '#ff0000',
    url: 'https://github.com/trending?l=BASIC'
  },
  batchfile: {
    color: '#C1F12E',
    url: 'https://github.com/trending?l=Batchfile'
  },
  beef: {
    color: '#a52f4e',
    url: 'https://github.com/trending?l=Beef'
  },
  berry: {
    color: '#15A13C',
    url: 'https://github.com/trending?l=Berry'
  },
  bibtex: {
    color: '#778899',
    url: 'https://github.com/trending?l=BibTeX'
  },
  bicep: {
    color: '#519aba',
    url: 'https://github.com/trending?l=Bicep'
  },
  bikeshed: {
    color: '#5562ac',
    url: 'https://github.com/trending?l=Bikeshed'
  },
  bison: {
    color: '#6A463F',
    url: 'https://github.com/trending?l=Bison'
  },
  bitbake: {
    color: '#00bce4',
    url: 'https://github.com/trending?l=BitBake'
  },
  blade: {
    color: '#f7523f',
    url: 'https://github.com/trending?l=Blade'
  },
  blitzbasic: {
    color: '#00FFAE',
    url: 'https://github.com/trending?l=BlitzBasic'
  },
  blitzmax: {
    color: '#cd6400',
    url: 'https://github.com/trending?l=BlitzMax'
  },
  bluespec: {
    color: '#12223c',
    url: 'https://github.com/trending?l=Bluespec'
  },
  boo: {
    color: '#d4bec1',
    url: 'https://github.com/trending?l=Boo'
  },
  boogie: {
    color: '#c80fa0',
    url: 'https://github.com/trending?l=Boogie'
  },
  brainfuck: {
    color: '#2F2530',
    url: 'https://github.com/trending?l=Brainfuck'
  },
  brighterscript: {
    color: '#66AABB',
    url: 'https://github.com/trending?l=BrighterScript'
  },
  brightscript: {
    color: '#662D91',
    url: 'https://github.com/trending?l=Brightscript'
  },
  browserslist: {
    color: '#ffd539',
    url: 'https://github.com/trending?l=Browserslist'
  },
  c: {
    color: '#555555',
    url: 'https://github.com/trending?l=C'
  },
  'c#': {
    color: '#178600',
    url: 'https://github.com/trending?l=Csharp'
  },
  'c++': {
    color: '#f34b7d',
    url: 'https://github.com/trending?l=C++'
  },
  'cabal config': {
    color: '#483465',
    url: 'https://github.com/trending?l=Cabal-Config'
  },
  cadence: {
    color: '#00ef8b',
    url: 'https://github.com/trending?l=Cadence'
  },
  cairo: {
    color: '#ff4a48',
    url: 'https://github.com/trending?l=Cairo'
  },
  cameligo: {
    color: '#3be133',
    url: 'https://github.com/trending?l=CameLIGO'
  },
  'cap cds': {
    color: '#0092d1',
    url: 'https://github.com/trending?l=CAP-CDS'
  },
  "cap'n proto": {
    color: '#c42727',
    url: "https://github.com/trending?l=Cap'n-Proto"
  },
  ceylon: {
    color: '#dfa535',
    url: 'https://github.com/trending?l=Ceylon'
  },
  chapel: {
    color: '#8dc63f',
    url: 'https://github.com/trending?l=Chapel'
  },
  chuck: {
    color: '#3f8000',
    url: 'https://github.com/trending?l=ChucK'
  },
  cirru: {
    color: '#ccccff',
    url: 'https://github.com/trending?l=Cirru'
  },
  clarion: {
    color: '#db901e',
    url: 'https://github.com/trending?l=Clarion'
  },
  clarity: {
    color: '#5546ff',
    url: 'https://github.com/trending?l=Clarity'
  },
  'classic asp': {
    color: '#6a40fd',
    url: 'https://github.com/trending?l=Classic-ASP'
  },
  clean: {
    color: '#3F85AF',
    url: 'https://github.com/trending?l=Clean'
  },
  click: {
    color: '#E4E6F3',
    url: 'https://github.com/trending?l=Click'
  },
  clips: {
    color: '#00A300',
    url: 'https://github.com/trending?l=CLIPS'
  },
  clojure: {
    color: '#db5855',
    url: 'https://github.com/trending?l=Clojure'
  },
  'closure templates': {
    color: '#0d948f',
    url: 'https://github.com/trending?l=Closure-Templates'
  },
  'cloud firestore security rules': {
    color: '#FFA000',
    url: 'https://github.com/trending?l=Cloud-Firestore-Security-Rules'
  },
  cmake: {
    color: '#DA3434',
    url: 'https://github.com/trending?l=CMake'
  },
  codeql: {
    color: '#140f46',
    url: 'https://github.com/trending?l=CodeQL'
  },
  coffeescript: {
    color: '#244776',
    url: 'https://github.com/trending?l=CoffeeScript'
  },
  coldfusion: {
    color: '#ed2cd6',
    url: 'https://github.com/trending?l=ColdFusion'
  },
  'coldfusion cfc': {
    color: '#ed2cd6',
    url: 'https://github.com/trending?l=ColdFusion-CFC'
  },
  collada: {
    color: '#F1A42B',
    url: 'https://github.com/trending?l=COLLADA'
  },
  'common lisp': {
    color: '#3fb68b',
    url: 'https://github.com/trending?l=Common-Lisp'
  },
  'common workflow language': {
    color: '#B5314C',
    url: 'https://github.com/trending?l=Common-Workflow-Language'
  },
  'component pascal': {
    color: '#B0CE4E',
    url: 'https://github.com/trending?l=Component-Pascal'
  },
  coq: {
    color: '#d0b68c',
    url: 'https://github.com/trending?l=Coq'
  },
  crystal: {
    color: '#000100',
    url: 'https://github.com/trending?l=Crystal'
  },
  cson: {
    color: '#244776',
    url: 'https://github.com/trending?l=CSON'
  },
  csound: {
    color: '#1a1a1a',
    url: 'https://github.com/trending?l=Csound'
  },
  'csound document': {
    color: '#1a1a1a',
    url: 'https://github.com/trending?l=Csound-Document'
  },
  'csound score': {
    color: '#1a1a1a',
    url: 'https://github.com/trending?l=Csound-Score'
  },
  css: {
    color: '#563d7c',
    url: 'https://github.com/trending?l=CSS'
  },
  csv: {
    color: '#237346',
    url: 'https://github.com/trending?l=CSV'
  },
  cuda: {
    color: '#3A4E3A',
    url: 'https://github.com/trending?l=Cuda'
  },
  cue: {
    color: '#5886E1',
    url: 'https://github.com/trending?l=CUE'
  },
  curry: {
    color: '#531242',
    url: 'https://github.com/trending?l=Curry'
  },
  cweb: {
    color: '#00007a',
    url: 'https://github.com/trending?l=CWeb'
  },
  cython: {
    color: '#fedf5b',
    url: 'https://github.com/trending?l=Cython'
  },
  d: {
    color: '#ba595e',
    url: 'https://github.com/trending?l=D'
  },
  dafny: {
    color: '#FFEC25',
    url: 'https://github.com/trending?l=Dafny'
  },
  'darcs patch': {
    color: '#8eff23',
    url: 'https://github.com/trending?l=Darcs-Patch'
  },
  dart: {
    color: '#00B4AB',
    url: 'https://github.com/trending?l=Dart'
  },
  dataweave: {
    color: '#003a52',
    url: 'https://github.com/trending?l=DataWeave'
  },
  'debian package control file': {
    color: '#D70751',
    url: 'https://github.com/trending?l=Debian-Package-Control-File'
  },
  denizenscript: {
    color: '#FBEE96',
    url: 'https://github.com/trending?l=DenizenScript'
  },
  dhall: {
    color: '#dfafff',
    url: 'https://github.com/trending?l=Dhall'
  },
  'directx 3d file': {
    color: '#aace60',
    url: 'https://github.com/trending?l=DirectX-3D-File'
  },
  dm: {
    color: '#447265',
    url: 'https://github.com/trending?l=DM'
  },
  dockerfile: {
    color: '#384d54',
    url: 'https://github.com/trending?l=Dockerfile'
  },
  dogescript: {
    color: '#cca760',
    url: 'https://github.com/trending?l=Dogescript'
  },
  dylan: {
    color: '#6c616e',
    url: 'https://github.com/trending?l=Dylan'
  },
  e: {
    color: '#ccce35',
    url: 'https://github.com/trending?l=E'
  },
  earthly: {
    color: '#2af0ff',
    url: 'https://github.com/trending?l=Earthly'
  },
  easybuild: {
    color: '#069406',
    url: 'https://github.com/trending?l=Easybuild'
  },
  ec: {
    color: '#913960',
    url: 'https://github.com/trending?l=eC'
  },
  'ecere projects': {
    color: '#913960',
    url: 'https://github.com/trending?l=Ecere-Projects'
  },
  ecl: {
    color: '#8a1267',
    url: 'https://github.com/trending?l=ECL'
  },
  eclipse: {
    color: '#001d9d',
    url: 'https://github.com/trending?l=ECLiPSe'
  },
  editorconfig: {
    color: '#fff1f2',
    url: 'https://github.com/trending?l=EditorConfig'
  },
  eiffel: {
    color: '#4d6977',
    url: 'https://github.com/trending?l=Eiffel'
  },
  ejs: {
    color: '#a91e50',
    url: 'https://github.com/trending?l=EJS'
  },
  elixir: {
    color: '#6e4a7e',
    url: 'https://github.com/trending?l=Elixir'
  },
  elm: {
    color: '#60B5CC',
    url: 'https://github.com/trending?l=Elm'
  },
  'emacs lisp': {
    color: '#c065db',
    url: 'https://github.com/trending?l=Emacs-Lisp'
  },
  emberscript: {
    color: '#FFF4F3',
    url: 'https://github.com/trending?l=EmberScript'
  },
  eq: {
    color: '#a78649',
    url: 'https://github.com/trending?l=EQ'
  },
  erlang: {
    color: '#B83998',
    url: 'https://github.com/trending?l=Erlang'
  },
  euphoria: {
    color: '#FF790B',
    url: 'https://github.com/trending?l=Euphoria'
  },
  'f#': {
    color: '#b845fc',
    url: 'https://github.com/trending?l=Fsharp'
  },
  'f*': {
    color: '#572e30',
    url: 'https://github.com/trending?l=F*'
  },
  factor: {
    color: '#636746',
    url: 'https://github.com/trending?l=Factor'
  },
  fancy: {
    color: '#7b9db4',
    url: 'https://github.com/trending?l=Fancy'
  },
  fantom: {
    color: '#14253c',
    url: 'https://github.com/trending?l=Fantom'
  },
  faust: {
    color: '#c37240',
    url: 'https://github.com/trending?l=Faust'
  },
  fennel: {
    color: '#fff3d7',
    url: 'https://github.com/trending?l=Fennel'
  },
  'figlet font': {
    color: '#FFDDBB',
    url: 'https://github.com/trending?l=FIGlet-Font'
  },
  'filebench wml': {
    color: '#F6B900',
    url: 'https://github.com/trending?l=Filebench-WML'
  },
  fish: {
    color: '#4aae47',
    url: 'https://github.com/trending?l=fish'
  },
  fluent: {
    color: '#ffcc33',
    url: 'https://github.com/trending?l=Fluent'
  },
  flux: {
    color: '#88ccff',
    url: 'https://github.com/trending?l=FLUX'
  },
  forth: {
    color: '#341708',
    url: 'https://github.com/trending?l=Forth'
  },
  fortran: {
    color: '#4d41b1',
    url: 'https://github.com/trending?l=Fortran'
  },
  'fortran free form': {
    color: '#4d41b1',
    url: 'https://github.com/trending?l=Fortran-Free-Form'
  },
  freebasic: {
    color: '#867db1',
    url: 'https://github.com/trending?l=FreeBasic'
  },
  freemarker: {
    color: '#0050b2',
    url: 'https://github.com/trending?l=FreeMarker'
  },
  frege: {
    color: '#00cafe',
    url: 'https://github.com/trending?l=Frege'
  },
  futhark: {
    color: '#5f021f',
    url: 'https://github.com/trending?l=Futhark'
  },
  'g-code': {
    color: '#D08CF2',
    url: 'https://github.com/trending?l=G-code'
  },
  'game maker language': {
    color: '#71b417',
    url: 'https://github.com/trending?l=Game-Maker-Language'
  },
  gaml: {
    color: '#FFC766',
    url: 'https://github.com/trending?l=GAML'
  },
  gams: {
    color: '#f49a22',
    url: 'https://github.com/trending?l=GAMS'
  },
  gap: {
    color: '#0000cc',
    url: 'https://github.com/trending?l=GAP'
  },
  'gcc machine description': {
    color: '#FFCFAB',
    url: 'https://github.com/trending?l=GCC-Machine-Description'
  },
  gdscript: {
    color: '#355570',
    url: 'https://github.com/trending?l=GDScript'
  },
  gedcom: {
    color: '#003058',
    url: 'https://github.com/trending?l=GEDCOM'
  },
  'gemfile.lock': {
    color: '#701516',
    url: 'https://github.com/trending?l=Gemfile.lock'
  },
  genero: {
    color: '#63408e',
    url: 'https://github.com/trending?l=Genero'
  },
  'genero forms': {
    color: '#d8df39',
    url: 'https://github.com/trending?l=Genero-Forms'
  },
  genie: {
    color: '#fb855d',
    url: 'https://github.com/trending?l=Genie'
  },
  genshi: {
    color: '#951531',
    url: 'https://github.com/trending?l=Genshi'
  },
  'gentoo ebuild': {
    color: '#9400ff',
    url: 'https://github.com/trending?l=Gentoo-Ebuild'
  },
  'gentoo eclass': {
    color: '#9400ff',
    url: 'https://github.com/trending?l=Gentoo-Eclass'
  },
  'gerber image': {
    color: '#d20b00',
    url: 'https://github.com/trending?l=Gerber-Image'
  },
  gherkin: {
    color: '#5B2063',
    url: 'https://github.com/trending?l=Gherkin'
  },
  'git attributes': {
    color: '#F44D27',
    url: 'https://github.com/trending?l=Git-Attributes'
  },
  'git config': {
    color: '#F44D27',
    url: 'https://github.com/trending?l=Git-Config'
  },
  'git revision list': {
    color: '#F44D27',
    url: 'https://github.com/trending?l=Git-Revision-List'
  },
  gleam: {
    color: '#ffaff3',
    url: 'https://github.com/trending?l=Gleam'
  },
  glsl: {
    color: '#5686a5',
    url: 'https://github.com/trending?l=GLSL'
  },
  glyph: {
    color: '#c1ac7f',
    url: 'https://github.com/trending?l=Glyph'
  },
  gnuplot: {
    color: '#f0a9f0',
    url: 'https://github.com/trending?l=Gnuplot'
  },
  go: {
    color: '#00ADD8',
    url: 'https://github.com/trending?l=Go'
  },
  'go checksums': {
    color: '#00ADD8',
    url: 'https://github.com/trending?l=Go-Checksums'
  },
  'go module': {
    color: '#00ADD8',
    url: 'https://github.com/trending?l=Go-Module'
  },
  golo: {
    color: '#88562A',
    url: 'https://github.com/trending?l=Golo'
  },
  gosu: {
    color: '#82937f',
    url: 'https://github.com/trending?l=Gosu'
  },
  grace: {
    color: '#615f8b',
    url: 'https://github.com/trending?l=Grace'
  },
  gradle: {
    color: '#02303a',
    url: 'https://github.com/trending?l=Gradle'
  },
  'grammatical framework': {
    color: '#ff0000',
    url: 'https://github.com/trending?l=Grammatical-Framework'
  },
  graphql: {
    color: '#e10098',
    url: 'https://github.com/trending?l=GraphQL'
  },
  'graphviz (dot)': {
    color: '#2596be',
    url: 'https://github.com/trending?l=Graphviz-(DOT)'
  },
  groovy: {
    color: '#4298b8',
    url: 'https://github.com/trending?l=Groovy'
  },
  'groovy server pages': {
    color: '#4298b8',
    url: 'https://github.com/trending?l=Groovy-Server-Pages'
  },
  gsc: {
    color: '#FF6800',
    url: 'https://github.com/trending?l=GSC'
  },
  hack: {
    color: '#878787',
    url: 'https://github.com/trending?l=Hack'
  },
  haml: {
    color: '#ece2a9',
    url: 'https://github.com/trending?l=Haml'
  },
  handlebars: {
    color: '#f7931e',
    url: 'https://github.com/trending?l=Handlebars'
  },
  haproxy: {
    color: '#106da9',
    url: 'https://github.com/trending?l=HAProxy'
  },
  harbour: {
    color: '#0e60e3',
    url: 'https://github.com/trending?l=Harbour'
  },
  haskell: {
    color: '#5e5086',
    url: 'https://github.com/trending?l=Haskell'
  },
  haxe: {
    color: '#df7900',
    url: 'https://github.com/trending?l=Haxe'
  },
  hcl: {
    color: '#0073b7',
    url: 'https://github.com/trending?l=HCL'
  },
  hiveql: {
    color: '#dce200',
    url: 'https://github.com/trending?l=HiveQL'
  },
  hlsl: {
    color: '#aace60',
    url: 'https://github.com/trending?l=HLSL'
  },
  holyc: {
    color: '#ffefaf',
    url: 'https://github.com/trending?l=HolyC'
  },
  hoon: {
    color: '#00b171',
    url: 'https://github.com/trending?l=hoon'
  },
  html: {
    color: '#e34c26',
    url: 'https://github.com/trending?l=HTML'
  },
  'html+ecr': {
    color: '#2e1052',
    url: 'https://github.com/trending?l=HTML+ECR'
  },
  'html+eex': {
    color: '#6e4a7e',
    url: 'https://github.com/trending?l=HTML+EEX'
  },
  'html+erb': {
    color: '#701516',
    url: 'https://github.com/trending?l=HTML+ERB'
  },
  'html+php': {
    color: '#4f5d95',
    url: 'https://github.com/trending?l=HTML+PHP'
  },
  'html+razor': {
    color: '#512be4',
    url: 'https://github.com/trending?l=HTML+Razor'
  },
  http: {
    color: '#005C9C',
    url: 'https://github.com/trending?l=HTTP'
  },
  hxml: {
    color: '#f68712',
    url: 'https://github.com/trending?l=HXML'
  },
  hy: {
    color: '#7790B2',
    url: 'https://github.com/trending?l=Hy'
  },
  idl: {
    color: '#a3522f',
    url: 'https://github.com/trending?l=IDL'
  },
  idris: {
    color: '#b30000',
    url: 'https://github.com/trending?l=Idris'
  },
  'ignore list': {
    color: '#000000',
    url: 'https://github.com/trending?l=Ignore-List'
  },
  'igor pro': {
    color: '#0000cc',
    url: 'https://github.com/trending?l=IGOR-Pro'
  },
  'imagej macro': {
    color: '#99AAFF',
    url: 'https://github.com/trending?l=ImageJ-Macro'
  },
  ini: {
    color: '#d1dbe0',
    url: 'https://github.com/trending?l=INI'
  },
  'inno setup': {
    color: '#264b99',
    url: 'https://github.com/trending?l=Inno-Setup'
  },
  io: {
    color: '#a9188d',
    url: 'https://github.com/trending?l=Io'
  },
  ioke: {
    color: '#078193',
    url: 'https://github.com/trending?l=Ioke'
  },
  isabelle: {
    color: '#FEFE00',
    url: 'https://github.com/trending?l=Isabelle'
  },
  'isabelle root': {
    color: '#FEFE00',
    url: 'https://github.com/trending?l=Isabelle-ROOT'
  },
  j: {
    color: '#9EEDFF',
    url: 'https://github.com/trending?l=J'
  },
  janet: {
    color: '#0886a5',
    url: 'https://github.com/trending?l=Janet'
  },
  'jar manifest': {
    color: '#b07219',
    url: 'https://github.com/trending?l=JAR-Manifest'
  },
  jasmin: {
    color: '#d03600',
    url: 'https://github.com/trending?l=Jasmin'
  },
  java: {
    color: '#b07219',
    url: 'https://github.com/trending?l=Java'
  },
  'java properties': {
    color: '#2A6277',
    url: 'https://github.com/trending?l=Java-Properties'
  },
  'java server pages': {
    color: '#2A6277',
    url: 'https://github.com/trending?l=Java-Server-Pages'
  },
  javascript: {
    color: '#f1e05a',
    url: 'https://github.com/trending?l=JavaScript'
  },
  'javascript+erb': {
    color: '#f1e05a',
    url: 'https://github.com/trending?l=JavaScript+ERB'
  },
  'jest snapshot': {
    color: '#15c213',
    url: 'https://github.com/trending?l=Jest-Snapshot'
  },
  'jetbrains mps': {
    color: '#21D789',
    url: 'https://github.com/trending?l=JetBrains-MPS'
  },
  jflex: {
    color: '#DBCA00',
    url: 'https://github.com/trending?l=JFlex'
  },
  jinja: {
    color: '#a52a22',
    url: 'https://github.com/trending?l=Jinja'
  },
  jison: {
    color: '#56b3cb',
    url: 'https://github.com/trending?l=Jison'
  },
  'jison lex': {
    color: '#56b3cb',
    url: 'https://github.com/trending?l=Jison-Lex'
  },
  jolie: {
    color: '#843179',
    url: 'https://github.com/trending?l=Jolie'
  },
  jq: {
    color: '#c7254e',
    url: 'https://github.com/trending?l=jq'
  },
  json: {
    color: '#292929',
    url: 'https://github.com/trending?l=JSON'
  },
  'json with comments': {
    color: '#292929',
    url: 'https://github.com/trending?l=JSON-with-Comments'
  },
  json5: {
    color: '#267CB9',
    url: 'https://github.com/trending?l=JSON5'
  },
  jsoniq: {
    color: '#40d47e',
    url: 'https://github.com/trending?l=JSONiq'
  },
  jsonld: {
    color: '#0c479c',
    url: 'https://github.com/trending?l=JSONLD'
  },
  jsonnet: {
    color: '#0064bd',
    url: 'https://github.com/trending?l=Jsonnet'
  },
  julia: {
    color: '#a270ba',
    url: 'https://github.com/trending?l=Julia'
  },
  'jupyter notebook': {
    color: '#DA5B0B',
    url: 'https://github.com/trending?l=Jupyter-Notebook'
  },
  'kaitai struct': {
    color: '#773b37',
    url: 'https://github.com/trending?l=Kaitai-Struct'
  },
  kakounescript: {
    color: '#6f8042',
    url: 'https://github.com/trending?l=KakouneScript'
  },
  'kicad layout': {
    color: '#2f4aab',
    url: 'https://github.com/trending?l=KiCad-Layout'
  },
  'kicad legacy layout': {
    color: '#2f4aab',
    url: 'https://github.com/trending?l=KiCad-Legacy-Layout'
  },
  'kicad schematic': {
    color: '#2f4aab',
    url: 'https://github.com/trending?l=KiCad-Schematic'
  },
  kotlin: {
    color: '#A97BFF',
    url: 'https://github.com/trending?l=Kotlin'
  },
  krl: {
    color: '#28430A',
    url: 'https://github.com/trending?l=KRL'
  },
  kvlang: {
    color: '#1da6e0',
    url: 'https://github.com/trending?l=kvlang'
  },
  labview: {
    color: '#fede06',
    url: 'https://github.com/trending?l=LabVIEW'
  },
  lark: {
    color: '#2980B9',
    url: 'https://github.com/trending?l=Lark'
  },
  lasso: {
    color: '#999999',
    url: 'https://github.com/trending?l=Lasso'
  },
  latte: {
    color: '#f2a542',
    url: 'https://github.com/trending?l=Latte'
  },
  less: {
    color: '#1d365d',
    url: 'https://github.com/trending?l=Less'
  },
  lex: {
    color: '#DBCA00',
    url: 'https://github.com/trending?l=Lex'
  },
  lfe: {
    color: '#4C3023',
    url: 'https://github.com/trending?l=LFE'
  },
  ligolang: {
    color: '#0e74ff',
    url: 'https://github.com/trending?l=LigoLANG'
  },
  lilypond: {
    color: '#9ccc7c',
    url: 'https://github.com/trending?l=LilyPond'
  },
  liquid: {
    color: '#67b8de',
    url: 'https://github.com/trending?l=Liquid'
  },
  'literate agda': {
    color: '#315665',
    url: 'https://github.com/trending?l=Literate-Agda'
  },
  'literate coffeescript': {
    color: '#244776',
    url: 'https://github.com/trending?l=Literate-CoffeeScript'
  },
  'literate haskell': {
    color: '#5e5086',
    url: 'https://github.com/trending?l=Literate-Haskell'
  },
  livescript: {
    color: '#499886',
    url: 'https://github.com/trending?l=LiveScript'
  },
  llvm: {
    color: '#185619',
    url: 'https://github.com/trending?l=LLVM'
  },
  logtalk: {
    color: '#295b9a',
    url: 'https://github.com/trending?l=Logtalk'
  },
  lolcode: {
    color: '#cc9900',
    url: 'https://github.com/trending?l=LOLCODE'
  },
  lookml: {
    color: '#652B81',
    url: 'https://github.com/trending?l=LookML'
  },
  lsl: {
    color: '#3d9970',
    url: 'https://github.com/trending?l=LSL'
  },
  lua: {
    color: '#000080',
    url: 'https://github.com/trending?l=Lua'
  },
  macaulay2: {
    color: '#d8ffff',
    url: 'https://github.com/trending?l=Macaulay2'
  },
  makefile: {
    color: '#427819',
    url: 'https://github.com/trending?l=Makefile'
  },
  mako: {
    color: '#7e858d',
    url: 'https://github.com/trending?l=Mako'
  },
  markdown: {
    color: '#083fa1',
    url: 'https://github.com/trending?l=Markdown'
  },
  marko: {
    color: '#42bff2',
    url: 'https://github.com/trending?l=Marko'
  },
  mask: {
    color: '#f97732',
    url: 'https://github.com/trending?l=Mask'
  },
  mathematica: {
    color: '#dd1100',
    url: 'https://github.com/trending?l=Mathematica'
  },
  matlab: {
    color: '#e16737',
    url: 'https://github.com/trending?l=MATLAB'
  },
  max: {
    color: '#c4a79c',
    url: 'https://github.com/trending?l=Max'
  },
  maxscript: {
    color: '#00a6a6',
    url: 'https://github.com/trending?l=MAXScript'
  },
  mcfunction: {
    color: '#E22837',
    url: 'https://github.com/trending?l=mcfunction'
  },
  mercury: {
    color: '#ff2b2b',
    url: 'https://github.com/trending?l=Mercury'
  },
  meson: {
    color: '#007800',
    url: 'https://github.com/trending?l=Meson'
  },
  metal: {
    color: '#8f14e9',
    url: 'https://github.com/trending?l=Metal'
  },
  miniyaml: {
    color: '#ff1111',
    url: 'https://github.com/trending?l=MiniYAML'
  },
  mint: {
    color: '#02b046',
    url: 'https://github.com/trending?l=Mint'
  },
  mirah: {
    color: '#c7a938',
    url: 'https://github.com/trending?l=Mirah'
  },
  'mirc script': {
    color: '#3d57c3',
    url: 'https://github.com/trending?l=mIRC-Script'
  },
  mlir: {
    color: '#5EC8DB',
    url: 'https://github.com/trending?l=MLIR'
  },
  modelica: {
    color: '#de1d31',
    url: 'https://github.com/trending?l=Modelica'
  },
  'modula-2': {
    color: '#10253f',
    url: 'https://github.com/trending?l=Modula-2'
  },
  'modula-3': {
    color: '#223388',
    url: 'https://github.com/trending?l=Modula-3'
  },
  'monkey c': {
    color: '#8D6747',
    url: 'https://github.com/trending?l=Monkey-C'
  },
  moonscript: {
    color: '#ff4585',
    url: 'https://github.com/trending?l=MoonScript'
  },
  motoko: {
    color: '#fbb03b',
    url: 'https://github.com/trending?l=Motoko'
  },
  'motorola 68k assembly': {
    color: '#005daa',
    url: 'https://github.com/trending?l=Motorola-68K-Assembly'
  },
  move: {
    color: '#4a137a',
    url: 'https://github.com/trending?l=Move'
  },
  mql4: {
    color: '#62A8D6',
    url: 'https://github.com/trending?l=MQL4'
  },
  mql5: {
    color: '#4A76B8',
    url: 'https://github.com/trending?l=MQL5'
  },
  mtml: {
    color: '#b7e1f4',
    url: 'https://github.com/trending?l=MTML'
  },
  mupad: {
    color: '#244963',
    url: 'https://github.com/trending?l=mupad'
  },
  mustache: {
    color: '#724b3b',
    url: 'https://github.com/trending?l=Mustache'
  },
  nanorc: {
    color: '#2d004d',
    url: 'https://github.com/trending?l=nanorc'
  },
  nasal: {
    color: '#1d2c4e',
    url: 'https://github.com/trending?l=Nasal'
  },
  ncl: {
    color: '#28431f',
    url: 'https://github.com/trending?l=NCL'
  },
  nearley: {
    color: '#990000',
    url: 'https://github.com/trending?l=Nearley'
  },
  nemerle: {
    color: '#3d3c6e',
    url: 'https://github.com/trending?l=Nemerle'
  },
  nesc: {
    color: '#94B0C7',
    url: 'https://github.com/trending?l=nesC'
  },
  netlinx: {
    color: '#0aa0ff',
    url: 'https://github.com/trending?l=NetLinx'
  },
  'netlinx+erb': {
    color: '#747faa',
    url: 'https://github.com/trending?l=NetLinx+ERB'
  },
  netlogo: {
    color: '#ff6375',
    url: 'https://github.com/trending?l=NetLogo'
  },
  newlisp: {
    color: '#87AED7',
    url: 'https://github.com/trending?l=NewLisp'
  },
  nextflow: {
    color: '#3ac486',
    url: 'https://github.com/trending?l=Nextflow'
  },
  nginx: {
    color: '#009639',
    url: 'https://github.com/trending?l=Nginx'
  },
  nim: {
    color: '#ffc200',
    url: 'https://github.com/trending?l=Nim'
  },
  nit: {
    color: '#009917',
    url: 'https://github.com/trending?l=Nit'
  },
  nix: {
    color: '#7e7eff',
    url: 'https://github.com/trending?l=Nix'
  },
  'npm config': {
    color: '#cb3837',
    url: 'https://github.com/trending?l=NPM-Config'
  },
  nu: {
    color: '#c9df40',
    url: 'https://github.com/trending?l=Nu'
  },
  numpy: {
    color: '#9C8AF9',
    url: 'https://github.com/trending?l=NumPy'
  },
  nunjucks: {
    color: '#3d8137',
    url: 'https://github.com/trending?l=Nunjucks'
  },
  nwscript: {
    color: '#111522',
    url: 'https://github.com/trending?l=NWScript'
  },
  'objective-c': {
    color: '#438eff',
    url: 'https://github.com/trending?l=Objective-C'
  },
  'objective-c++': {
    color: '#6866fb',
    url: 'https://github.com/trending?l=Objective-C++'
  },
  'objective-j': {
    color: '#ff0c5a',
    url: 'https://github.com/trending?l=Objective-J'
  },
  objectscript: {
    color: '#424893',
    url: 'https://github.com/trending?l=ObjectScript'
  },
  ocaml: {
    color: '#3be133',
    url: 'https://github.com/trending?l=OCaml'
  },
  odin: {
    color: '#60AFFE',
    url: 'https://github.com/trending?l=Odin'
  },
  omgrofl: {
    color: '#cabbff',
    url: 'https://github.com/trending?l=Omgrofl'
  },
  ooc: {
    color: '#b0b77e',
    url: 'https://github.com/trending?l=ooc'
  },
  opal: {
    color: '#f7ede0',
    url: 'https://github.com/trending?l=Opal'
  },
  'open policy agent': {
    color: '#7d9199',
    url: 'https://github.com/trending?l=Open-Policy-Agent'
  },
  opencl: {
    color: '#ed2e2d',
    url: 'https://github.com/trending?l=OpenCL'
  },
  'openedge abl': {
    color: '#5ce600',
    url: 'https://github.com/trending?l=OpenEdge-ABL'
  },
  openqasm: {
    color: '#AA70FF',
    url: 'https://github.com/trending?l=OpenQASM'
  },
  openscad: {
    color: '#e5cd45',
    url: 'https://github.com/trending?l=OpenSCAD'
  },
  org: {
    color: '#77aa99',
    url: 'https://github.com/trending?l=Org'
  },
  oxygene: {
    color: '#cdd0e3',
    url: 'https://github.com/trending?l=Oxygene'
  },
  oz: {
    color: '#fab738',
    url: 'https://github.com/trending?l=Oz'
  },
  p4: {
    color: '#7055b5',
    url: 'https://github.com/trending?l=P4'
  },
  pan: {
    color: '#cc0000',
    url: 'https://github.com/trending?l=Pan'
  },
  papyrus: {
    color: '#6600cc',
    url: 'https://github.com/trending?l=Papyrus'
  },
  parrot: {
    color: '#f3ca0a',
    url: 'https://github.com/trending?l=Parrot'
  },
  pascal: {
    color: '#E3F171',
    url: 'https://github.com/trending?l=Pascal'
  },
  pawn: {
    color: '#dbb284',
    url: 'https://github.com/trending?l=Pawn'
  },
  'peg.js': {
    color: '#234d6b',
    url: 'https://github.com/trending?l=PEG.js'
  },
  pep8: {
    color: '#C76F5B',
    url: 'https://github.com/trending?l=Pep8'
  },
  perl: {
    color: '#0298c3',
    url: 'https://github.com/trending?l=Perl'
  },
  php: {
    color: '#4F5D95',
    url: 'https://github.com/trending?l=PHP'
  },
  picolisp: {
    color: '#6067af',
    url: 'https://github.com/trending?l=PicoLisp'
  },
  piglatin: {
    color: '#fcd7de',
    url: 'https://github.com/trending?l=PigLatin'
  },
  pike: {
    color: '#005390',
    url: 'https://github.com/trending?l=Pike'
  },
  plpgsql: {
    color: '#336790',
    url: 'https://github.com/trending?l=PLpgSQL'
  },
  plsql: {
    color: '#dad8d8',
    url: 'https://github.com/trending?l=PLSQL'
  },
  pogoscript: {
    color: '#d80074',
    url: 'https://github.com/trending?l=PogoScript'
  },
  portugol: {
    color: '#f8bd00',
    url: 'https://github.com/trending?l=Portugol'
  },
  postcss: {
    color: '#dc3a0c',
    url: 'https://github.com/trending?l=PostCSS'
  },
  postscript: {
    color: '#da291c',
    url: 'https://github.com/trending?l=PostScript'
  },
  'pov-ray sdl': {
    color: '#6bac65',
    url: 'https://github.com/trending?l=POV-Ray-SDL'
  },
  powerbuilder: {
    color: '#8f0f8d',
    url: 'https://github.com/trending?l=PowerBuilder'
  },
  powershell: {
    color: '#012456',
    url: 'https://github.com/trending?l=PowerShell'
  },
  prisma: {
    color: '#0c344b',
    url: 'https://github.com/trending?l=Prisma'
  },
  processing: {
    color: '#0096D8',
    url: 'https://github.com/trending?l=Processing'
  },
  procfile: {
    color: '#3B2F63',
    url: 'https://github.com/trending?l=Procfile'
  },
  prolog: {
    color: '#74283c',
    url: 'https://github.com/trending?l=Prolog'
  },
  promela: {
    color: '#de0000',
    url: 'https://github.com/trending?l=Promela'
  },
  'propeller spin': {
    color: '#7fa2a7',
    url: 'https://github.com/trending?l=Propeller-Spin'
  },
  pug: {
    color: '#a86454',
    url: 'https://github.com/trending?l=Pug'
  },
  puppet: {
    color: '#302B6D',
    url: 'https://github.com/trending?l=Puppet'
  },
  purebasic: {
    color: '#5a6986',
    url: 'https://github.com/trending?l=PureBasic'
  },
  purescript: {
    color: '#1D222D',
    url: 'https://github.com/trending?l=PureScript'
  },
  python: {
    color: '#3572A5',
    url: 'https://github.com/trending?l=Python'
  },
  'python console': {
    color: '#3572A5',
    url: 'https://github.com/trending?l=Python-console'
  },
  'python traceback': {
    color: '#3572A5',
    url: 'https://github.com/trending?l=Python-traceback'
  },
  q: {
    color: '#0040cd',
    url: 'https://github.com/trending?l=q'
  },
  'q#': {
    color: '#fed659',
    url: 'https://github.com/trending?l=Qsharp'
  },
  qml: {
    color: '#44a51c',
    url: 'https://github.com/trending?l=QML'
  },
  'qt script': {
    color: '#00b841',
    url: 'https://github.com/trending?l=Qt-Script'
  },
  quake: {
    color: '#882233',
    url: 'https://github.com/trending?l=Quake'
  },
  r: {
    color: '#198CE7',
    url: 'https://github.com/trending?l=R'
  },
  racket: {
    color: '#3c5caa',
    url: 'https://github.com/trending?l=Racket'
  },
  ragel: {
    color: '#9d5200',
    url: 'https://github.com/trending?l=Ragel'
  },
  raku: {
    color: '#0000fb',
    url: 'https://github.com/trending?l=Raku'
  },
  raml: {
    color: '#77d9fb',
    url: 'https://github.com/trending?l=RAML'
  },
  rascal: {
    color: '#fffaa0',
    url: 'https://github.com/trending?l=Rascal'
  },
  rdoc: {
    color: '#701516',
    url: 'https://github.com/trending?l=RDoc'
  },
  reason: {
    color: '#ff5847',
    url: 'https://github.com/trending?l=Reason'
  },
  reasonligo: {
    color: '#ff5847',
    url: 'https://github.com/trending?l=ReasonLIGO'
  },
  rebol: {
    color: '#358a5b',
    url: 'https://github.com/trending?l=Rebol'
  },
  'record jar': {
    color: '#0673ba',
    url: 'https://github.com/trending?l=Record-Jar'
  },
  red: {
    color: '#f50000',
    url: 'https://github.com/trending?l=Red'
  },
  'regular expression': {
    color: '#009a00',
    url: 'https://github.com/trending?l=Regular-Expression'
  },
  "ren'py": {
    color: '#ff7f7f',
    url: "https://github.com/trending?l=Ren'Py"
  },
  rescript: {
    color: '#ed5051',
    url: 'https://github.com/trending?l=ReScript'
  },
  restructuredtext: {
    color: '#141414',
    url: 'https://github.com/trending?l=reStructuredText'
  },
  rexx: {
    color: '#d90e09',
    url: 'https://github.com/trending?l=REXX'
  },
  ring: {
    color: '#2D54CB',
    url: 'https://github.com/trending?l=Ring'
  },
  riot: {
    color: '#A71E49',
    url: 'https://github.com/trending?l=Riot'
  },
  rmarkdown: {
    color: '#198ce7',
    url: 'https://github.com/trending?l=RMarkdown'
  },
  robotframework: {
    color: '#00c0b5',
    url: 'https://github.com/trending?l=RobotFramework'
  },
  roff: {
    color: '#ecdebe',
    url: 'https://github.com/trending?l=Roff'
  },
  'roff manpage': {
    color: '#ecdebe',
    url: 'https://github.com/trending?l=Roff-Manpage'
  },
  rouge: {
    color: '#cc0088',
    url: 'https://github.com/trending?l=Rouge'
  },
  'routeros script': {
    color: '#DE3941',
    url: 'https://github.com/trending?l=RouterOS-Script'
  },
  rpgle: {
    color: '#2BDE21',
    url: 'https://github.com/trending?l=RPGLE'
  },
  ruby: {
    color: '#701516',
    url: 'https://github.com/trending?l=Ruby'
  },
  runoff: {
    color: '#665a4e',
    url: 'https://github.com/trending?l=RUNOFF'
  },
  rust: {
    color: '#dea584',
    url: 'https://github.com/trending?l=Rust'
  },
  saltstack: {
    color: '#646464',
    url: 'https://github.com/trending?l=SaltStack'
  },
  sas: {
    color: '#B34936',
    url: 'https://github.com/trending?l=SAS'
  },
  sass: {
    color: '#a53b70',
    url: 'https://github.com/trending?l=Sass'
  },
  scala: {
    color: '#c22d40',
    url: 'https://github.com/trending?l=Scala'
  },
  scaml: {
    color: '#bd181a',
    url: 'https://github.com/trending?l=Scaml'
  },
  scheme: {
    color: '#1e4aec',
    url: 'https://github.com/trending?l=Scheme'
  },
  scilab: {
    color: '#ca0f21',
    url: 'https://github.com/trending?l=Scilab'
  },
  scss: {
    color: '#c6538c',
    url: 'https://github.com/trending?l=SCSS'
  },
  sed: {
    color: '#64b970',
    url: 'https://github.com/trending?l=sed'
  },
  self: {
    color: '#0579aa',
    url: 'https://github.com/trending?l=Self'
  },
  shaderlab: {
    color: '#222c37',
    url: 'https://github.com/trending?l=ShaderLab'
  },
  shell: {
    color: '#89e051',
    url: 'https://github.com/trending?l=Shell'
  },
  'shellcheck config': {
    color: '#cecfcb',
    url: 'https://github.com/trending?l=ShellCheck-Config'
  },
  shen: {
    color: '#120F14',
    url: 'https://github.com/trending?l=Shen'
  },
  singularity: {
    color: '#64E6AD',
    url: 'https://github.com/trending?l=Singularity'
  },
  slash: {
    color: '#007eff',
    url: 'https://github.com/trending?l=Slash'
  },
  slice: {
    color: '#003fa2',
    url: 'https://github.com/trending?l=Slice'
  },
  slim: {
    color: '#2b2b2b',
    url: 'https://github.com/trending?l=Slim'
  },
  smalltalk: {
    color: '#596706',
    url: 'https://github.com/trending?l=Smalltalk'
  },
  smarty: {
    color: '#f0c040',
    url: 'https://github.com/trending?l=Smarty'
  },
  smpl: {
    color: '#c94949',
    url: 'https://github.com/trending?l=SmPL'
  },
  solidity: {
    color: '#AA6746',
    url: 'https://github.com/trending?l=Solidity'
  },
  sourcepawn: {
    color: '#f69e1d',
    url: 'https://github.com/trending?l=SourcePawn'
  },
  sparql: {
    color: '#0C4597',
    url: 'https://github.com/trending?l=SPARQL'
  },
  sqf: {
    color: '#3F3F3F',
    url: 'https://github.com/trending?l=SQF'
  },
  sql: {
    color: '#e38c00',
    url: 'https://github.com/trending?l=SQL'
  },
  sqlpl: {
    color: '#e38c00',
    url: 'https://github.com/trending?l=SQLPL'
  },
  squirrel: {
    color: '#800000',
    url: 'https://github.com/trending?l=Squirrel'
  },
  'srecode template': {
    color: '#348a34',
    url: 'https://github.com/trending?l=SRecode-Template'
  },
  stan: {
    color: '#b2011d',
    url: 'https://github.com/trending?l=Stan'
  },
  'standard ml': {
    color: '#dc566d',
    url: 'https://github.com/trending?l=Standard-ML'
  },
  starlark: {
    color: '#76d275',
    url: 'https://github.com/trending?l=Starlark'
  },
  stata: {
    color: '#1a5f91',
    url: 'https://github.com/trending?l=Stata'
  },
  stl: {
    color: '#373b5e',
    url: 'https://github.com/trending?l=STL'
  },
  stringtemplate: {
    color: '#3fb34f',
    url: 'https://github.com/trending?l=StringTemplate'
  },
  stylus: {
    color: '#ff6347',
    url: 'https://github.com/trending?l=Stylus'
  },
  'subrip text': {
    color: '#9e0101',
    url: 'https://github.com/trending?l=SubRip-Text'
  },
  sugarss: {
    color: '#2fcc9f',
    url: 'https://github.com/trending?l=SugarSS'
  },
  supercollider: {
    color: '#46390b',
    url: 'https://github.com/trending?l=SuperCollider'
  },
  svelte: {
    color: '#ff3e00',
    url: 'https://github.com/trending?l=Svelte'
  },
  svg: {
    color: '#ff9900',
    url: 'https://github.com/trending?l=SVG'
  },
  swift: {
    color: '#F05138',
    url: 'https://github.com/trending?l=Swift'
  },
  systemverilog: {
    color: '#DAE1C2',
    url: 'https://github.com/trending?l=SystemVerilog'
  },
  talon: {
    color: '#333333',
    url: 'https://github.com/trending?l=Talon'
  },
  tcl: {
    color: '#e4cc98',
    url: 'https://github.com/trending?l=Tcl'
  },
  terra: {
    color: '#00004c',
    url: 'https://github.com/trending?l=Terra'
  },
  tex: {
    color: '#3D6117',
    url: 'https://github.com/trending?l=TeX'
  },
  textile: {
    color: '#ffe7ac',
    url: 'https://github.com/trending?l=Textile'
  },
  'textmate properties': {
    color: '#df66e4',
    url: 'https://github.com/trending?l=TextMate-Properties'
  },
  thrift: {
    color: '#D12127',
    url: 'https://github.com/trending?l=Thrift'
  },
  'ti program': {
    color: '#A0AA87',
    url: 'https://github.com/trending?l=TI-Program'
  },
  tla: {
    color: '#4b0079',
    url: 'https://github.com/trending?l=TLA'
  },
  toml: {
    color: '#9c4221',
    url: 'https://github.com/trending?l=TOML'
  },
  tsql: {
    color: '#e38c00',
    url: 'https://github.com/trending?l=TSQL'
  },
  tsv: {
    color: '#237346',
    url: 'https://github.com/trending?l=TSV'
  },
  tsx: {
    color: '#3178c6',
    url: 'https://github.com/trending?l=TSX'
  },
  turing: {
    color: '#cf142b',
    url: 'https://github.com/trending?l=Turing'
  },
  twig: {
    color: '#c1d026',
    url: 'https://github.com/trending?l=Twig'
  },
  txl: {
    color: '#0178b8',
    url: 'https://github.com/trending?l=TXL'
  },
  typescript: {
    color: '#3178c6',
    url: 'https://github.com/trending?l=TypeScript'
  },
  'unified parallel c': {
    color: '#4e3617',
    url: 'https://github.com/trending?l=Unified-Parallel-C'
  },
  'unity3d asset': {
    color: '#222c37',
    url: 'https://github.com/trending?l=Unity3D-Asset'
  },
  uno: {
    color: '#9933cc',
    url: 'https://github.com/trending?l=Uno'
  },
  unrealscript: {
    color: '#a54c4d',
    url: 'https://github.com/trending?l=UnrealScript'
  },
  urweb: {
    color: '#ccccee',
    url: 'https://github.com/trending?l=UrWeb'
  },
  v: {
    color: '#4f87c4',
    url: 'https://github.com/trending?l=V'
  },
  vala: {
    color: '#a56de2',
    url: 'https://github.com/trending?l=Vala'
  },
  'valve data format': {
    color: '#f26025',
    url: 'https://github.com/trending?l=Valve-Data-Format'
  },
  vba: {
    color: '#867db1',
    url: 'https://github.com/trending?l=VBA'
  },
  vbscript: {
    color: '#15dcdc',
    url: 'https://github.com/trending?l=VBScript'
  },
  vcl: {
    color: '#148AA8',
    url: 'https://github.com/trending?l=VCL'
  },
  'velocity template language': {
    color: '#507cff',
    url: 'https://github.com/trending?l=Velocity-Template-Language'
  },
  verilog: {
    color: '#b2b7f8',
    url: 'https://github.com/trending?l=Verilog'
  },
  vhdl: {
    color: '#adb2cb',
    url: 'https://github.com/trending?l=VHDL'
  },
  'vim help file': {
    color: '#199f4b',
    url: 'https://github.com/trending?l=Vim-Help-File'
  },
  'vim script': {
    color: '#199f4b',
    url: 'https://github.com/trending?l=Vim-Script'
  },
  'vim snippet': {
    color: '#199f4b',
    url: 'https://github.com/trending?l=Vim-Snippet'
  },
  'visual basic .net': {
    color: '#945db7',
    url: 'https://github.com/trending?l=Visual-Basic-.NET'
  },
  volt: {
    color: '#1F1F1F',
    url: 'https://github.com/trending?l=Volt'
  },
  vue: {
    color: '#41b883',
    url: 'https://github.com/trending?l=Vue'
  },
  vyper: {
    color: '#2980b9',
    url: 'https://github.com/trending?l=Vyper'
  },
  wdl: {
    color: '#42f1f4',
    url: 'https://github.com/trending?l=wdl'
  },
  'web ontology language': {
    color: '#5b70bd',
    url: 'https://github.com/trending?l=Web-Ontology-Language'
  },
  webassembly: {
    color: '#04133b',
    url: 'https://github.com/trending?l=WebAssembly'
  },
  whiley: {
    color: '#d5c397',
    url: 'https://github.com/trending?l=Whiley'
  },
  wikitext: {
    color: '#fc5757',
    url: 'https://github.com/trending?l=Wikitext'
  },
  'windows registry entries': {
    color: '#52d5ff',
    url: 'https://github.com/trending?l=Windows-Registry-Entries'
  },
  wisp: {
    color: '#7582D1',
    url: 'https://github.com/trending?l=wisp'
  },
  'witcher script': {
    color: '#ff0000',
    url: 'https://github.com/trending?l=Witcher-Script'
  },
  wollok: {
    color: '#a23738',
    url: 'https://github.com/trending?l=Wollok'
  },
  'world of warcraft addon data': {
    color: '#f7e43f',
    url: 'https://github.com/trending?l=World-of-Warcraft-Addon-Data'
  },
  wren: {
    color: '#383838',
    url: 'https://github.com/trending?l=Wren'
  },
  x10: {
    color: '#4B6BEF',
    url: 'https://github.com/trending?l=X10'
  },
  xbase: {
    color: '#403a40',
    url: 'https://github.com/trending?l=xBase'
  },
  xc: {
    color: '#99DA07',
    url: 'https://github.com/trending?l=XC'
  },
  xml: {
    color: '#0060ac',
    url: 'https://github.com/trending?l=XML'
  },
  'xml property list': {
    color: '#0060ac',
    url: 'https://github.com/trending?l=XML-Property-List'
  },
  xojo: {
    color: '#81bd41',
    url: 'https://github.com/trending?l=Xojo'
  },
  xonsh: {
    color: '#285EEF',
    url: 'https://github.com/trending?l=Xonsh'
  },
  xquery: {
    color: '#5232e7',
    url: 'https://github.com/trending?l=XQuery'
  },
  xslt: {
    color: '#EB8CEB',
    url: 'https://github.com/trending?l=XSLT'
  },
  xtend: {
    color: '#24255d',
    url: 'https://github.com/trending?l=Xtend'
  },
  yacc: {
    color: '#4B6C4B',
    url: 'https://github.com/trending?l=Yacc'
  },
  yaml: {
    color: '#cb171e',
    url: 'https://github.com/trending?l=YAML'
  },
  yara: {
    color: '#220000',
    url: 'https://github.com/trending?l=YARA'
  },
  yasnippet: {
    color: '#32AB90',
    url: 'https://github.com/trending?l=YASnippet'
  },
  yul: {
    color: '#794932',
    url: 'https://github.com/trending?l=Yul'
  },
  zap: {
    color: '#0d665e',
    url: 'https://github.com/trending?l=ZAP'
  },
  zenscript: {
    color: '#00BCD1',
    url: 'https://github.com/trending?l=ZenScript'
  },
  zephir: {
    color: '#118f9e',
    url: 'https://github.com/trending?l=Zephir'
  },
  zig: {
    color: '#ec915c',
    url: 'https://github.com/trending?l=Zig'
  },
  zil: {
    color: '#dc75e5',
    url: 'https://github.com/trending?l=ZIL'
  },
  zimpl: {
    color: '#d67711',
    url: 'https://github.com/trending?l=Zimpl'
  }
};
