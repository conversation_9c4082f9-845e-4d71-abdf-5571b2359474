import { handleApi } from '@/api-helpers/axios-api-instance';
import { flattenObj } from '@/utils/datatype';
import { debounce } from '@/utils/debounce';
import { objectEnum } from '@/utils/enum';

enum TrackEventEnum {
  TRIAL_BOX_EXPANDED,
  TRIAL_BOX_COLLAPSED,
  ORG_TREE_ADD,
  ORG_TREE_REMOVE,
  ERR_FALLBACK_SHOWN,
  LOCAL_ERR_FALLBACK_SHOWN,
  USER_ADDED_STANDALONE,
  BADGE_FOCUS,
  ONE_ON_ONE_LINK_COPIED,
  ONE_ON_ONE_NOTES_USED,
  ORG_CHART_GUIDE_SEEN,
  ORG_CHART_DROP_EVENT,
  ORG_CHART_DRAG_EVENT,
  WINDOW_FOCUS,
  WINDOW_BLUR,
  WINDOW_UNLOAD,
  SESSION_FETCH,
  OOO_INSIGHTS_EXPANDED,
  OOO_INSIGHTS_COLLAPSED,
  OOO_GROWTH_EXPANDED,
  OOO_GROWTH_COLLAPSED,
  CAREER_UPDATE,
  G<PERSON><PERSON><PERSON>_UPDATE,
  ACTION_ITEMS_UPDATE,
  AGENDA_ITEMS_UPDATE,
  FEEDBACK_ITEMS_UPDATE,
  COMMENT_UPSERT,
  COMMENT_DELETE,
  UPSERT_OOO_FREQUENCY,
  OOO_DONE,
  TRIAL_EXTENSION_CONTACT_CLICK,
  PR_NON_REVIEWED_LIST_VIEWED,
  OOO_WITH_SELF,
  EMPTY_STATE_CONTACT_CLICK,
  EMPTY_STATE_VIEWED,
  EMPTY_STATE_NO_TEAM_MEMBERS,
  JIRA_CONFLICTS_PRESENT,
  JIRA_CONFLICTS_ABSENT,
  JIRA_CONFLICTS_RESOLVED,
  JIRA_TICKETS_LIST_VIEWED,
  JIRA_RETRO_VIEWED,
  JIRA_HEALTH_TICKET_SPILL_AGAIN,
  JIRA_HEALTH_DIRECT_SUCESSFUL_TICKETS,
  JIRA_PILL_TOOLTIP_VIEWED,
  CHART_NODE_TOOLTIP_VIEWED,
  JIRA_PROJECT_TOGGLED,
  JIRA_PROJECTS_CONFIG_SAVED,
  ERR_BOUNDARY_PAGE_RELOAD,
  ERR_BOUNDARY_RESET_UI,
  ERR_BOUNDARY_GO_HOME,
  CADENCE_USER_MISSING,
  OVERLAY_ADD_PAGE,
  OVERLAY_ADD_PAGE_IGNORED,
  OVERLAY_UPDATE_PAGE,
  OVERLAY_REMOVE_PAGE,
  OVERLAY_REMOVE_ALL_PAGES,
  EXPERTS_VIEW_LANG,
  EXPERTS_VIEW_HOME,
  EXPERTS_VIEW_REPO,
  OOO_TEMPLATE_VIEW_IN_RECORD,
  OOO_TEMPLATE_EDIT_TRIGGER,
  OOO_TEMPLATE_EDIT_COMPLETE,
  OOO_TEMPLATE_DELETE_TRIGGER,
  OOO_TEMPLATE_DELETE_COMPLETE,
  OOO_TEMPLATE_DEFAULT_TRIGGER,
  OOO_TEMPLATE_DEFAULT_COMPLETE,
  LOGIN_ERR_TRIAL_EXPIRED,
  LOGIN_ERR_PENDING_APPROVAL,
  LOGIN_ERR_ORG_DENYLISTED,
  LOGIN_ERR_GENERIC,
  LOGIN_ERR_ACT_TOAST_CLOSED,
  SOMETHING_WENT_WRONG,
  INTEGRATION_LINK_TRIGGERED,
  INTEGRATION_UNLINK_TRIGGERED,
  TEAM_PROJECT_ADD_TRIGGERED,
  TEAM_PROJECT_REMOVE_TRIGGERED,
  TEAM_PROJECT_SAVED,
  TEAM_REPO_ADD_TRIGGERED,
  TEAM_REPO_REMOVE_TRIGGERED,
  TEAM_REPO_SAVED,
  ORG_REPO_ENABLED,
  ORG_REPO_DISABLED,
  ORG_REPO_SAVED,
  ERR_TOAST_SHOWN,
  JIRA_MAINTENANCE_TOOLTIP_SHOWN,
  APP_TEAM_CHANGE_SINGLE,
  APP_TEAM_TOGGLE_MULTI,
  APP_TEAM_CHANGE_AUTO,
  TEAM_CSV_TPL_DOWNLOAD,
  GENERIC_FILE_UPLOADED,
  WINDOW_RESIZE,
  MAINTENANCE_MODE_SHOWN,
  BRING_BACK_MIDDLEWARE_CLICKED,
  BTC_MODAL_OPEN,
  BTC_MODAL_CLOSE,
  BTC_MODAL_AUTO_CLOSE,
  BTC_ERR_INVALID_HEADER,
  BTC_ERR_NO_ROWS,
  BTC_ERR_API_VALIDATION,
  BTC_SUCCESSFUL_PRE_UPLOAD,
  BTC_SUCCESSFUL_TEAM_CREATION,
  PAID_PLAN_REQUEST_EMAIL,
  OOO_CAREER_AND_CODE_EXPANDED,
  OOO_CAREER_AND_CODE_COLLAPSED,
  VIEW_WORKFLOWS_FOR_REPO,
  SELECT_WORKFLOWS_FOR_REPO,
  DEPLOYMENT_CLASSIFICATION_VIEWED,
  OOO_REPORTEE_FILTER_CHANGE,
  OOO_REPORTEE_MODAL_OPENED,
  OOO_BASE_USER_CARD_START_1ON1_CLICKED,
  OOO_USER_PIN_TOGGLED,
  OOO_REPORTEE_UI_EXPANSION_TOGGLED,
  OOO_HISTORY_USER_CHANGE,
  OOO_HISTORY_REPORTEE_FILTER_CHANGE,
  EMPTY_STATE_OOO_HISTORY,
  USER_REMOVED_AS_ADMIN,
  USER_ADDED_AS_ADMIN,
  USER_OPENED_ADMINS_DROPDOWN,
  PERSONAL_NOTIFICATION_SETTINGS_CHANGED,
  ORG_NOTIFICATION_SETTINGS_CHANGED,
  USER_DELETED,
  USER_EDITED,
  USER_RESTORED,
  METRICS_TAB_SWITCHED,
  DEV_INSIGHTS_TAB_SWITCHED,
  SPRINT_FILTER_TOGGLED,
  JIRA_STATE_TICKET_VIEWED,
  ZENDUTY_SERVICE_TOGGLED,
  ZENDUTY_SERVICES_CONFIG_SAVED,
  PAGERDUTY_SERVICE_TOGGLED,
  PAGERDUTY_SERVICES_CONFIG_SAVED,
  UNSPECIFIED_PROVIDER_SERVICE_TOGGLED,
  UNSPECIFIED_PROVIDER_SERVICES_CONFIG_SAVE_ATTEMPT,
  TEAM_INCIDENT_SERVICES_SAVED,
  TEAM_INCIDENT_SERVICES_REMOVE_TRIGGERED,
  TEAM_INCIDENT_SERVICES_ADD_TRIGGERED,
  DORA_METRICS_SEE_DETAILS_CLICKED,
  NAVIGATED_TO_INTEGRATIONS_FROM_MISSING_DORA_LINK,
  FEEDBACK_CYCLE_DELETED,
  FEEDBACK_CYCLE_PUBLISHED,
  FEEDBACK_CYCLE_DRAFT_SAVED,
  PROJECT_FILTER_TOGGLED,
  VIDEO_ONBOARDING_STARTED,
  VIDEO_ONBOARDING_COMPLETED,
  VIDEO_ONBOARDING_NEXT,
  VIDEO_ONBOARDING_PREVIOUS,
  VIDEO_ONBOARDING_RESET_ALL,
  VIDEO_ONBOARDING_DONE_ALL,
  VIDEO_ONBOARDING_FAB_CLICKED,
  VIDEO_ONBOARDING_POPOVER_EXPANDED,
  VIDEO_ONBOARDING_MODAL_OPEN,
  SP_METRICS_TAB_SWITCHED,
  SP_INSIGHTS_CTA_CLICKED,
  SP_TRENDS_CHECKBOX_TOGGLED,
  EXPLORE_LANGUAGES_TEAM_EXPERTS,
  EXPLORE_REPOSITORIES_TEAM_EXPERTS,
  SEE_STATS_FOR_ALL_PRS_CLICKED,
  CHANGE_TIME_TAB_SWITCHED,
  APP_DATE_RANGE_CHANGED,
  HOVER_ON_CHANGE_TIME_QUICK_STATS_CHART,
  LINK_JIRA_USERS_TRIGGERED,
  TEAM_EDIT_ACTION_CLICKED,
  TEAM_DELETE_ACTION_CLICKED,
  TEAM_LEAVE_ACTION_CLICKED,
  TEAM_JOIN_ACTION_CLICKED,
  TEAM_CREATION_TRIGGERED,
  OOO_TEMPLATE_USED,
  DORA_METRICS_DATE_RANGE_CHANGED,
  PROJECT_MANAGEMENT_DATE_RANGE_CHANGED,
  PROCESS_OVERVIEW_DATE_RANGE_CHANGED,
  DORA_METRICS_TEAM_CHANGE_SINGLE,
  PROJECT_MANAGEMENT_TEAM_CHANGE_SINGLE,
  PROCESS_OVERVIEW_TEAM_CHANGE_SINGLE,
  TEAM_EXPERTS_TEAM_CHANGE_SINGLE,
  TEAM_EXPERTS_DATE_RANGE_CHANGED,
  USER_OPENED_PLAYBOOK_RULE_USERS_DROPDOWN,
  USER_UPDATED_FROM_PLAYBOOK_RULE_USERS_DROPDOWN,
  PLAYBOOK_UPDATED_FOR_TEAM,
  OPENAI_CONSENT_GRANTED,
  OPENAI_CONSENT_DENIED,
  AI_SPRINT_ANALYSIS_MODAL_OPENED,
  OPENAI_RESPONSE_INACCURACY,
  CHANGE_TIME_STATS_MODE_SWITCHED,
  AI_ANALYSIS_IMPROVEMENT_NEEDED,
  AI_ANALYSIS_GOOD_FEEDBACK,
  AI_ANALYSIS_RATING_SUBMITTED,
  ADHOC_CARRY_OVER_TRENDS_CLICKED,
  AI_SPRINT_ANALYSIS_MODAL_CLOSE_ATTEMPT,
  AI_SPRINT_ANALYSIS_MODAL_CLOSE_CONFIRM,
  AI_SPRINT_ANALYSIS_MODAL_CLOSE_CANCEL,
  TEAM_PROD_BRANCH_SELECTOR_OPENED,
  TEAM_PROD_BRANCH_SELECTOR_CLOSED,
  TEAM_PROD_BRANCH_CONF_SAVE_STARTED,
  TEAM_PROD_BRANCH_CONF_SAVE_SUCCESS,
  TEAM_PROD_BRANCH_CONF_SAVE_FAILURE,
  TEAM_PROD_BRANCH_SELECTOR_ERRORED,
  DETAILED_GUIDE_ONBOARDING_LINK_CLICKED,
  AI_MODAL_BTN_LINGER,
  GENERATE_SHARE_LINK,
  GENERATE_LINK_ERROR,
  COPIED_GENERATED_LINK,
  LOAD_SHARE_LINK,
  LOAD_INVALID_SHARE_LINK,
  FEEDBACK_CYCLE_MARKED_AS_COMPLETE,
  HOVER_ON_DELIVERY_FLOW_CHART,
  OPENED_COCKPIT_METRIC_MODAL,
  COCKPIT_TEAMS_MANAGERS_SWITCH_TOGGLED,
  TEAM_NAVIGATION_TOAST_CLICKED,
  MANAGER_NAVIGATION_TOAST_CLICKED,
  CLICKED_COCKPIT_PROJECT_FLOW_CHIP,
  USER_VISIT_REASON_SUBMITTED,
  USER_VISIT_REASON_REQUESTED,
  USER_VISIT_REASON_MANUALLY_CLOSED,
  USER_VISIT_REASON_AUTO_CLOSED,
  USER_VISIT_REASON_SELECTED,
  COCKPIT_METRIC_CONFIG_SAVE_SUCCESS,
  COCKPIT_METRIC_CONFIG_SAVE_FAILURE,
  APPLIED_DATE_RANGE,
  EMAIL_DIGEST_SETTINGS_CHANGED,
  CSV_EXPORT_TRIGGERED
}

export const TrackEvents = objectEnum(TrackEventEnum);

export const track = (ev: keyof typeof TrackEvents, props: any = {}) => {
  dbTrack.add(ev, props);
};

class BatchTrack {
  private dataToBeTracked: {
    activity_type: keyof typeof TrackEvents;
    activity_data: any;
  }[] = [];

  private track = debounce(() => {
    try {
      handleApi('/internal/track', {
        data: { data: this.dataToBeTracked },
        method: 'POST'
      });
    } catch (e) {}

    this.dataToBeTracked = [];
  }, 5000);

  add = (ev: keyof typeof TrackEvents, props: any = {}) => {
    this.dataToBeTracked.push({
      activity_type: ev,
      activity_data: flattenObj(props)
    });

    this.track();
  };
}

const dbTrack = new BatchTrack();
