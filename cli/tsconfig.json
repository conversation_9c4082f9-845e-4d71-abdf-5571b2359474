{"compilerOptions": {"outDir": "dist", "allowJs": true, "allowSyntheticDefaultImports": true, "jsx": "react-jsx", "module": "Node16", "moduleResolution": "Node16", "moduleDetection": "force", "target": "ES2022", "lib": ["DOM", "DOM.Iterable", "ES2022"], "resolveJsonModule": false, "declaration": true, "pretty": true, "newLine": "lf", "stripInternal": true, "strict": true, "noImplicitReturns": true, "noImplicitOverride": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noPropertyAccessFromIndexSignature": true, "noEmitOnError": true, "useDefineForClassFields": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "strictNullChecks": true}, "include": ["source"]}