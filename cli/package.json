{"name": "cli", "version": "0.0.0", "license": "MIT", "bin": "dist/cli.js", "engines": {"node": "^22.x"}, "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "yarn link-fix && ava", "lint": "eslint source/**/*.{js,ts,tsx}", "lint-fix": "eslint --fix --ignore-pattern 'node_modules/*' 'source/**/*.{js,ts,tsx}'", "start": "[ -e ../.env ] && set -a && . ../.env; node dist/cli.js"}, "files": ["dist"], "dependencies": {"@reduxjs/toolkit": "^2.2.3", "dockerode": "^4.0.2", "dockerode-compose": "^1.4.0", "find-free-ports": "^3.1.1", "ink": "^4.4.1", "ink-spinner": "^5.0.0", "meow": "^11.0.0", "ramda": "^0.29.1", "react": "^18.2.0", "react-redux": "^9.1.1"}, "devDependencies": {"@types/dockerode": "^3.3.28", "@types/dockerode-compose": "^1.4.1", "@types/ink-testing-library": "^1.0.4", "@types/ramda": "^0.29.12", "@types/react": "^18.0.32", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "ava": "^5.2.0", "chalk": "^5.2.0", "eslint": "^8.40.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^3.1.0", "ink-testing-library": "^3.0.0", "prettier": "^3.0.3", "ts-node": "^10.9.1", "typescript": "5.3.3"}, "ava": {"extensions": {"ts": "module", "tsx": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}}