name: Black Integration

on:
  pull_request:
    branches: ["main"]
    paths:
      - 'backend/**'
      - '**/*.py'

permissions:
  contents: read

jobs:
  black:
    name: Black Check
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          path: 'apiserver' # Only checkout the api folder

      - name: Set up Python 3.11.6
        uses: actions/setup-python@v3
        with:
          python-version: "3.11.6"

      - name: Install Black
        run: python -m pip install black==24.3.0

      - name: Run Black Check
        run: black . --check
