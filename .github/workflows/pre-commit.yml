name: Linting-Precommit

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  pre_job:
    name: Path match check
    runs-on: ubuntu-latest
    outputs:
      should_skip: ${{ steps.skip_check.outputs.should_skip }}
    steps:
      - id: skip_check
        uses: fkirc/skip-duplicate-actions@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          paths_ignore: '["**.po", "**.json"]'

  linting:
    name: All file linting
    needs: pre_job
    if: ${{ needs.pre_job.outputs.should_skip != 'true' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js 22
        uses: actions/setup-node@v3
        with:
          node-version: 22

      - name: Install dependencies for cli
        run: yarn add eslint@^8.40.0 eslint-plugin-import@^2.29.0 eslint-plugin-prettier@^5.0.1 eslint-plugin-react@^7.32.2 eslint-plugin-unused-imports@^3.1.0 --dev
        working-directory: cli

      - name: Install dependencies for web-server
        run: yarn add eslint@^8.40.0 eslint-config-next@15.0.1 eslint-plugin-import@^2.29.0 eslint-plugin-prettier@^5.0.1 eslint-plugin-react@^7.32.2 eslint-plugin-unused-imports@^3.1.0 --dev
        working-directory: web-server

      - name: Setup Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Create and activate virtual environment
        run: |
          python -m venv venv
          source venv/bin/activate

      - name: Install pre-commit
        run: |
          source venv/bin/activate
          python -m pip install pre-commit

      - name: Run pre-commit hooks
        uses: pre-commit/action@v3.0.1
        with:
          extra_args: --all-files
