#!/bin/bash

# Service Management Script for VM
# Manages multiple services running on the same VM

echo "🔧 VM Service Management"
echo "======================="

# Function to show service status
show_status() {
    echo ""
    echo "📊 Current Service Status:"
    echo "-------------------------"
    
    echo "🐳 Docker Containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | head -10
    
    echo ""
    echo "🌐 Port Usage:"
    echo "Port 80 (HTTP): $(sudo netstat -tulpn | grep ':80 ' | awk '{print $7}' | cut -d/ -f2)"
    echo "Port 443 (HTTPS): $(sudo netstat -tulpn | grep ':443 ' | awk '{print $7}' | cut -d/ -f2)"
    echo "Port 3000 (LibreChat): $(sudo netstat -tulpn | grep ':3000 ' | awk '{print $7}' | cut -d/ -f2)"
    echo "Port 3333 (Middleware): $(sudo netstat -tulpn | grep ':3333 ' | awk '{print $7}' | cut -d/ -f2)"
    echo "Port 8000 (Airbyte): $(sudo netstat -tulpn | grep ':8000 ' | awk '{print $7}' | cut -d/ -f2)"
    echo "Port 9696 (Middleware API): $(sudo netstat -tulpn | grep ':9696 ' | awk '{print $7}' | cut -d/ -f2)"
    echo "Port 9697 (Middleware Sync): $(sudo netstat -tulpn | grep ':9697 ' | awk '{print $7}' | cut -d/ -f2)"
}

# Function to stop specific services
stop_service() {
    local service=$1
    case $service in
        "librechat")
            echo "🛑 Stopping LibreChat and related services..."
            docker stop LibreChat rag_api chat-mongodb vectordb chat-meilisearch 2>/dev/null || true
            echo "✅ LibreChat services stopped"
            ;;
        "airbyte")
            echo "🛑 Stopping Airbyte..."
            docker stop airbyte-abctl-control-plane 2>/dev/null || true
            echo "✅ Airbyte stopped"
            ;;
        "middleware")
            echo "🛑 Stopping Middleware..."
            docker stop middleware 2>/dev/null || true
            echo "✅ Middleware stopped"
            ;;
        "nginx")
            echo "🛑 Stopping Nginx..."
            sudo systemctl stop nginx
            echo "✅ Nginx stopped"
            ;;
        "all")
            echo "🛑 Stopping all services..."
            docker stop LibreChat rag_api chat-mongodb vectordb chat-meilisearch airbyte-abctl-control-plane middleware 2>/dev/null || true
            sudo systemctl stop nginx
            echo "✅ All services stopped"
            ;;
        *)
            echo "❌ Unknown service: $service"
            echo "Available services: librechat, airbyte, middleware, nginx, all"
            ;;
    esac
}

# Function to start specific services
start_service() {
    local service=$1
    case $service in
        "librechat")
            echo "▶️ Starting LibreChat and related services..."
            docker start chat-mongodb vectordb chat-meilisearch rag_api LibreChat 2>/dev/null || true
            echo "✅ LibreChat services started"
            ;;
        "airbyte")
            echo "▶️ Starting Airbyte..."
            docker start airbyte-abctl-control-plane 2>/dev/null || true
            echo "✅ Airbyte started"
            ;;
        "middleware")
            echo "▶️ Starting Middleware..."
            docker start middleware 2>/dev/null || true
            echo "✅ Middleware started"
            ;;
        "nginx")
            echo "▶️ Starting Nginx..."
            sudo systemctl start nginx
            echo "✅ Nginx started"
            ;;
        "all")
            echo "▶️ Starting all services..."
            docker start chat-mongodb vectordb chat-meilisearch rag_api LibreChat airbyte-abctl-control-plane middleware 2>/dev/null || true
            sudo systemctl start nginx
            echo "✅ All services started"
            ;;
        *)
            echo "❌ Unknown service: $service"
            echo "Available services: librechat, airbyte, middleware, nginx, all"
            ;;
    esac
}

# Function to restart specific services
restart_service() {
    local service=$1
    stop_service $service
    sleep 2
    start_service $service
}

# Main menu
case "${1:-status}" in
    "status"|"")
        show_status
        ;;
    "stop")
        if [ -z "$2" ]; then
            echo "Usage: $0 stop <service>"
            echo "Services: librechat, airbyte, middleware, nginx, all"
            exit 1
        fi
        stop_service $2
        ;;
    "start")
        if [ -z "$2" ]; then
            echo "Usage: $0 start <service>"
            echo "Services: librechat, airbyte, middleware, nginx, all"
            exit 1
        fi
        start_service $2
        ;;
    "restart")
        if [ -z "$2" ]; then
            echo "Usage: $0 restart <service>"
            echo "Services: librechat, airbyte, middleware, nginx, all"
            exit 1
        fi
        restart_service $2
        ;;
    "middleware-only")
        echo "🎯 Setting up for Middleware only..."
        echo "Stopping other services to free up resources..."
        stop_service librechat
        stop_service airbyte
        start_service middleware
        start_service nginx
        echo "✅ Middleware-only mode activated"
        ;;
    "help")
        echo "Usage: $0 [command] [service]"
        echo ""
        echo "Commands:"
        echo "  status          - Show current service status (default)"
        echo "  stop <service>  - Stop specific service"
        echo "  start <service> - Start specific service"
        echo "  restart <service> - Restart specific service"
        echo "  middleware-only - Stop other services, keep only Middleware"
        echo "  help           - Show this help"
        echo ""
        echo "Services:"
        echo "  librechat  - LibreChat + MongoDB + Meilisearch + VectorDB"
        echo "  airbyte    - Airbyte control plane"
        echo "  middleware - Middleware DORA metrics"
        echo "  nginx      - Nginx web server"
        echo "  all        - All services"
        echo ""
        echo "Examples:"
        echo "  $0 status"
        echo "  $0 stop librechat"
        echo "  $0 start middleware"
        echo "  $0 middleware-only"
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo "Run '$0 help' for usage information"
        exit 1
        ;;
esac
