FROM middlewareeng/oss-base:latest

ARG ENVIRONMENT=dev
ARG POSTGRES_DB_ENABLED=true
ARG DB_INIT_ENABLED=true
ARG REDIS_ENABLED=true
ARG BACKEND_ENABLED=true
ARG FRONTEND_ENABLED=true
ARG CRON_ENABLED=true
ARG DB_PORT=5434
ARG BUILD_DATE
ARG MERGE_COMMIT_SHA

ENV DB_HOST=localhost
ENV DB_NAME=mhq-oss
ENV DB_PASS=postgres
ENV DB_PORT=${DB_PORT}
ENV DB_USER=postgres
ENV REDIS_HOST=localhost
ENV REDIS_PORT=6385
ENV PORT=3333
ENV SYNC_SERVER_PORT=9697
ENV ANALYTICS_SERVER_PORT=9696
ENV INTERNAL_API_BASE_URL=http://localhost:9696
ENV INTERNAL_SYNC_API_BASE_URL=http://localhost:9697
ENV NEXT_PUBLIC_APP_ENVIRONMENT="development"
ENV PATH="/opt/venv/bin:/usr/lib/postgresql/15/bin:/usr/local/bin:$PATH"
ENV BUILD_DATE=$BUILD_DATE
ENV MERGE_COMMIT_SHA=$MERGE_COMMIT_SHA

WORKDIR /app
COPY . /app/

WORKDIR /app/backend
RUN python3 -m venv /opt/venv
RUN /opt/venv/bin/pip install --upgrade pip
RUN --mount=type=cache,target=/root/.cache/pip /opt/venv/bin/pip install -r requirements.txt -r dev-requirements.txt

WORKDIR /app
RUN mkdir -p /etc/cron.d && mv /app/setup_utils/cronjob.txt /etc/cron.d/cronjob
RUN chmod +x /app/setup_utils/start.sh /app/setup_utils/init_db.sh /app/setup_utils/generate_config_ini.sh
RUN mv /app/setup_utils/supervisord.conf /etc/supervisord.conf
RUN mv /app/database-docker/db/ /app/ && rm -rf /app/database-docker/
RUN echo "host all  all    0.0.0.0/0  md5" >> /etc/postgresql/15/main/pg_hba.conf
RUN echo "listen_addresses='*'" >> /etc/postgresql/15/main/postgresql.conf
RUN sed -i "s/^port = .*/port = ${DB_PORT}/" /etc/postgresql/15/main/postgresql.conf
RUN npm install --global yarn --force
RUN mkdir -p /var/log/postgres
RUN touch /var/log/postgres/postgres.log
RUN mkdir -p /var/log/init_db
RUN touch /var/log/init_db/init_db.log
RUN mkdir -p /var/log/redis
RUN touch /var/log/redis/redis.log
RUN mkdir -p /var/log/apiserver
RUN touch /var/log/apiserver/apiserver.log
RUN mkdir -p /var/log/sync_server
RUN touch /var/log/sync_server/sync_server.log
RUN mkdir -p /var/log/web-server
RUN touch /var/log/web-server/web-server.log
RUN mkdir -p /var/log/cron
RUN touch /var/log/cron/cron.log
RUN chmod 0644 /etc/cron.d/cronjob
RUN crontab /etc/cron.d/cronjob
RUN /app/setup_utils/generate_config_ini.sh -t /app/backend/analytics_server/mhq/config

WORKDIR /app/web-server
RUN --mount=type=cache,target=/root/.yarn YARN_CACHE_FOLDER=/root/.yarn yarn install --frozen-lockfile


ENV POSTGRES_DB_ENABLED=true
ENV DB_INIT_ENABLED=true
ENV REDIS_ENABLED=true
ENV BACKEND_ENABLED=true
ENV FRONTEND_ENABLED=true
ENV CRON_ENABLED=true
ENV ENVIRONMENT=dev
ENV BUILD_DATE=$BUILD_DATE
ENV MERGE_COMMIT_SHA=$MERGE_COMMIT_SHA

CMD ["/bin/bash", "-c", "/app/setup_utils/start.sh"]
