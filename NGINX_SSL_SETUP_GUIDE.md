# 🌐 Nginx + SSL Setup Guide for Middleware

This guide will help you set up nginx as a reverse proxy with SSL certificates for your Middleware instance at `dormetrics.truxt.ai`.

## 📋 Prerequisites

- ✅ Middleware container running on port 3333
- ✅ Domain `dormetrics.truxt.ai` pointing to your server's IP
- ✅ Root/sudo access to the server
- ✅ Ports 80 and 443 available

## 🚀 Quick Setup (Automated)

### Step 1: Run the Nginx Setup Script

```bash
sudo ./setup-nginx-ssl.sh
```

This script will:
- Install nginx and certbot if not already installed
- Create nginx configuration for your domain
- Set up reverse proxy to Middleware
- Configure security headers
- Enable the site

### Step 2: Configure DNS

**IMPORTANT**: Before proceeding, make sure your domain points to this server:

```bash
# Check current DNS resolution
dig +short dormetrics.truxt.ai

# Check your server's public IP
curl ifconfig.me
```

The domain should resolve to your server's IP address.

### Step 3: Obtain SSL Certificate

```bash
sudo ./setup-ssl-certificate.sh
```

This script will:
- Verify DNS resolution
- Obtain SSL certificate from Let's Encrypt
- Configure automatic renewal
- Set up HTTPS redirect

### Step 4: Verify Setup

```bash
./check-setup.sh
```

This will verify that everything is working correctly.

## 🔧 Manual Setup (Step by Step)

If you prefer to set up manually or need to troubleshoot:

### 1. Install Required Packages

```bash
sudo apt update
sudo apt install -y nginx certbot python3-certbot-nginx
```

### 2. Create Nginx Configuration

```bash
sudo cp nginx-middleware.conf /etc/nginx/sites-available/dormetrics.truxt.ai
sudo ln -s /etc/nginx/sites-available/dormetrics.truxt.ai /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
```

### 3. Test and Start Nginx

```bash
sudo nginx -t
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 4. Obtain SSL Certificate

```bash
sudo certbot --nginx --non-interactive --agree-tos \
  --email <EMAIL> \
  --domains dormetrics.truxt.ai \
  --redirect
```

### 5. Set Up Auto-Renewal

```bash
sudo crontab -e
# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

## 🌐 Access Your Middleware

Once setup is complete, you can access your Middleware at:

- **Main Application**: https://dormetrics.truxt.ai
- **Analytics API**: https://dormetrics.truxt.ai/api/
- **Sync API**: https://dormetrics.truxt.ai/sync-api/

## 🔍 Configuration Details

### Nginx Configuration Features

- ✅ **SSL/TLS**: Automatic HTTPS redirect
- ✅ **Security Headers**: XSS protection, HSTS, CSP
- ✅ **Reverse Proxy**: Routes to Middleware ports
- ✅ **Static File Caching**: Optimized for performance
- ✅ **Gzip Compression**: Reduced bandwidth usage
- ✅ **WebSocket Support**: For real-time features
- ✅ **CORS Headers**: For API access

### Port Mapping

| Service | Internal Port | External Access |
|---------|---------------|-----------------|
| Web UI | 3333 | https://dormetrics.truxt.ai/ |
| Analytics API | 9696 | https://dormetrics.truxt.ai/api/ |
| Sync API | 9697 | https://dormetrics.truxt.ai/sync-api/ |

## 🛠️ Troubleshooting

### Common Issues

**1. Domain doesn't resolve**
```bash
# Check DNS
dig +short dormetrics.truxt.ai
# Update your DNS records to point to this server
```

**2. Middleware not responding**
```bash
# Check if Middleware is running
docker ps | grep middleware
# Start if needed
docker start middleware
```

**3. Nginx configuration errors**
```bash
# Test configuration
sudo nginx -t
# Check logs
sudo tail -f /var/log/nginx/error.log
```

**4. SSL certificate issues**
```bash
# Check certificate status
sudo certbot certificates
# Test renewal
sudo certbot renew --dry-run
```

### Useful Commands

```bash
# Check all services
./check-setup.sh

# View Middleware logs
docker logs -f middleware

# View Nginx logs
sudo tail -f /var/log/nginx/dormetrics.truxt.ai.access.log
sudo tail -f /var/log/nginx/dormetrics.truxt.ai.error.log

# Restart services
sudo systemctl restart nginx
docker restart middleware

# Check SSL certificate
sudo certbot certificates

# Renew SSL certificate
sudo certbot renew

# Test nginx configuration
sudo nginx -t

# Reload nginx (without restart)
sudo systemctl reload nginx
```

## 🔐 Security Considerations

### Implemented Security Features

- **HTTPS Only**: All HTTP traffic redirected to HTTPS
- **HSTS**: HTTP Strict Transport Security enabled
- **Security Headers**: XSS protection, content type sniffing protection
- **Access Logs**: All requests logged for monitoring
- **Rate Limiting**: Can be added if needed

### Additional Security (Optional)

```bash
# Install fail2ban for additional protection
sudo apt install fail2ban

# Configure firewall (if not already done)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

## 📊 Monitoring

### Log Locations

- **Nginx Access**: `/var/log/nginx/dormetrics.truxt.ai.access.log`
- **Nginx Error**: `/var/log/nginx/dormetrics.truxt.ai.error.log`
- **Middleware**: `docker logs middleware`
- **SSL Renewal**: `/var/log/letsencrypt/letsencrypt.log`

### Health Checks

```bash
# Check if everything is working
curl -I https://dormetrics.truxt.ai

# Check API endpoints
curl -I https://dormetrics.truxt.ai/api/
curl -I https://dormetrics.truxt.ai/sync-api/
```

## 🔄 Maintenance

### Regular Tasks

1. **Monitor SSL expiry**: Certificates auto-renew, but check monthly
2. **Update packages**: Keep nginx and certbot updated
3. **Check logs**: Monitor for any errors or issues
4. **Backup configuration**: Keep copies of nginx configs

### SSL Certificate Renewal

Certificates automatically renew via cron job, but you can manually renew:

```bash
sudo certbot renew
sudo systemctl reload nginx
```

## 🎉 Success!

If everything is set up correctly, you should now have:

- ✅ Middleware accessible at https://dormetrics.truxt.ai
- ✅ Automatic HTTPS redirect
- ✅ SSL certificate with auto-renewal
- ✅ Optimized nginx configuration
- ✅ Security headers enabled
- ✅ Proper logging and monitoring

Your DORA metrics platform is now ready for production use! 🚀
