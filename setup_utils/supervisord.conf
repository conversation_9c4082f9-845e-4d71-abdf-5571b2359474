[supervisord]
nodaemon=true

[program:postgres]
priority=1
user=postgres
command=/bin/bash -c "postgres -D /var/lib/postgresql/15/main -c config_file=/etc/postgresql/15/main/postgresql.conf"
startsecs=10
stdout_logfile=/var/log/postgres/postgres.log
stdout_logfile_maxbytes=512KB
stderr_logfile=/var/log/postgres/postgres.log
stderr_logfile_maxbytes=512KB
stdout_logfile_backups=0
stderr_logfile_backups=0
autorestart=false
environment=POSTGRES_DB_ENABLED=%(ENV_POSTGRES_DB_ENABLED)s
autostart=%(ENV_POSTGRES_DB_ENABLED)s

[program:initialize_db]
priority=2
command=/app/setup_utils/init_db.sh
directory=/app
stdout_logfile=/var/log/init_db/init_db.log
stdout_logfile_maxbytes=512KB
stderr_logfile=/var/log/init_db/init_db.log
stderr_logfile_maxbytes=512KB
stdout_logfile_backups=0
stderr_logfile_backups=0
autorestart=false
environment=POSTGRES_DB_ENABLED=%(ENV_POSTGRES_DB_ENABLED)s
autostart=%(ENV_POSTGRES_DB_ENABLED)s

[program:redis]
priority=3
command=redis-server --port %(ENV_REDIS_PORT)s --protected-mode no
startsecs=10
stdout_logfile=/var/log/redis/redis.log
stdout_logfile_maxbytes=512KB
stderr_logfile=/var/log/redis/redis.log
stderr_logfile_maxbytes=512KB
stdout_logfile_backups=0
stderr_logfile_backups=0
autorestart=true
environment=REDIS_ENABLED=%(ENV_REDIS_ENABLED)s,REDIS_PORT=%(ENV_REDIS_PORT)s
autostart=%(ENV_REDIS_ENABLED)s

[program:backend]
priority=4
command=/bin/bash -c "chmod +x ./start_api_server.sh && ./start_api_server.sh"
directory=/app/setup_utils
startsecs=10
stdout_logfile=/var/log/apiserver/apiserver.log
stdout_logfile_maxbytes=512KB
stderr_logfile=/var/log/apiserver/apiserver.log
stderr_logfile_maxbytes=512KB
stdout_logfile_backups=0
stderr_logfile_backups=0
autorestart=true
retry=3
retry_delay=5
environment=BACKEND_ENABLED=%(ENV_BACKEND_ENABLED)s
autostart=%(ENV_BACKEND_ENABLED)s

[program:backend_sync]
priority=5
command=/bin/bash -c "chmod +x ./start_sync_server.sh && ./start_sync_server.sh"
directory=/app/setup_utils
startsecs=10
stdout_logfile=/var/log/sync_server/sync_server.log
stdout_logfile_maxbytes=512KB
stderr_logfile=/var/log/sync_server/sync_server.log
stderr_logfile_maxbytes=512KB
stdout_logfile_backups=0
stderr_logfile_backups=0
autorestart=true
retry=3
retry_delay=5
environment=BACKEND_ENABLED=%(ENV_BACKEND_ENABLED)s
autostart=%(ENV_BACKEND_ENABLED)s

[program:frontend]
command=/bin/bash -c "chmod +x ./start_frontend.sh && ./start_frontend.sh"
directory=/app/setup_utils
startsecs=10
stdout_logfile=/var/log/web-server/web-server.log
stdout_logfile_maxbytes=512KB
stderr_logfile=/var/log/web-server/web-server.log
stderr_logfile_maxbytes=512KB
stdout_logfile_backups=0
stderr_logfile_backups=0
autorestart=true
retry=3
retry_delay=5
environment=FRONTEND_ENABLED=%(ENV_FRONTEND_ENABLED)s
autostart=%(ENV_FRONTEND_ENABLED)s

[program:cron]
command=/bin/bash -c " exec /usr/sbin/cron -f -l 1"
autorestart=true
startsecs=10
stderr_logfile=/var/log/cron/cron.log
stdout_logfile=/var/log/cron/cron.log
stdout_logfile_maxbytes=512KB
stderr_logfile_maxbytes=512KB
stdout_logfile_backups=0
stderr_logfile_backups=0
retry=3
retry_delay=5
environment=CRON_ENABLED=%(ENV_CRON_ENABLED)s
autostart=%(ENV_CRON_ENABLED)s
