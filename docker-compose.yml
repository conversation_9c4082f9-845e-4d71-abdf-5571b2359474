volumes:
  dev_postgres_data:
    driver: local
  dev_keys:
    driver: local

services:
  middleware-dev:
    container_name: middleware-dev
    build:
      context: ./
      dockerfile: Dockerfile.dev
      args:
        ENVIRONMENT: ${ENVIRONMENT}
        POSTGRES_DB_ENABLED: ${POSTGRES_DB_ENABLED:-true}
        DB_INIT_ENABLED: ${DB_INIT_ENABLED:-true}
        REDIS_ENABLED: ${REDIS_ENABLED:-true}
        BACKEND_ENABLED: ${BACKEND_ENABLED:-true}
        FRONTEND_ENABLED: ${FRONTEND_ENABLED:-true}
        CRON_ENABLED: ${CRON_ENABLED:-true}
        DB_PORT: ${DB_PORT:-5434}

    env_file:
      - .env

    ports:
      - "127.0.0.1:${ANALYTICS_SERVER_PORT:-9696}:${ANALYTICS_SERVER_PORT:-9696}"
      - "127.0.0.1:${SYNC_SERVER_PORT:-9697}:${SYNC_SERVER_PORT:-9697}"
      - "127.0.0.1:${PORT:-3333}:${PORT:-3333}"
      - "127.0.0.1:${DB_PORT:-5434}:${DB_PORT:-5434}"
      - "127.0.0.1:${REDIS_PORT:-6385}:${REDIS_PORT:-6385}"

    extra_hosts:
      - "host.docker.internal:host-gateway"

    volumes:
      - dev_postgres_data:/var/lib/postgresql/15/main
      - dev_keys:/app/backend/analytics_server/mhq/config

    develop:
      watch:
        - action: sync
          path: ./backend/analytics_server
          target: /app/backend/analytics_server
          ignore:
            - venv
            - __pycache__
            - env.example

        - action: rebuild
          path: ./backend/requirements.txt

        - action: rebuild
          path: ./backend/dev-requirements.txt

        - action: sync+restart
          path: ./setup_utils/supervisord.conf
          target: /etc/supervisord.conf

        - action: rebuild
          path: ./setup_utils/init_db.sh

        - action: rebuild
          path: ./.env

        - action: sync+restart
          path: ./backend/analytics_server/.env.local
          target: /app/backend/analytics_server/.env.local

        - action: sync
          path: ./web-server
          target: /app/web-server
          ignore:
            - ./web-server/.vscode
            - ./web-server/node_modules

        - action: rebuild
          path: ./web-server/package.json

        - action: sync+restart
          path: ./setup_utils/
          target: /app/setup_utils
